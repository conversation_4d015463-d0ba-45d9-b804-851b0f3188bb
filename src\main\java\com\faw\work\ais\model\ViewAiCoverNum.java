package com.faw.work.ais.model;

import lombok.Data;

import java.util.Date;

@Data
public class ViewAiCoverNum {
    /**
     * id
     */
    private Integer id;

    /**
     * AI覆盖角色数
     */
    private Integer aiCoverRole;

    /**
     * AI覆盖规则数
     */
    private Integer aiCoverRule;

    /**
     * AI覆盖业务单元数
     */
    private Integer aiCoverBizUnit;

    /**
     * 处理人
     */
    private String dealName;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 上线满足
     */
    private Integer isReadyFor;

    /**
     * 是否显示真实准确率
     */
    private Integer isTrueCover;
}