package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * FAQ机器人请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "FAQ机器人请求对象")
public class FaqRobotSubmitRequest {

    @Schema(description = "主键ID")
    private String id;
    
    @NotBlank(message = "机器人名称不能为空")
    @Schema(description = "机器人名称")
    private String robotName;

    @Schema(description = "类目id集合")
    private List<String> categoryId;
} 