package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.entity.dto.ai.AiTaskDTO;
import com.faw.work.ais.entity.dto.ai.FixBugRequest;
import com.faw.work.ais.mapper.ais.FixBugMapper;
import com.faw.work.ais.service.AiTaskService;
import com.faw.work.ais.service.FixBugService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Objects;

/**
 * 问题修复 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-17 14:51
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FixBugServiceImpl implements FixBugService {

    private final AiTaskService aiTaskService;

    private final FixBugMapper fixBugMapper;


    @Override
    public String subsidyFixBug(FixBugRequest request) {
        log.info("[FixBugService][subsidyFixBug][entrance] request: {}", JSON.toJSONString(request));
        if (Objects.isNull(request) || CollectionUtils.isEmpty(request.getBidList())) {
            return "列表空";
        }

        for (String s : request.getBidList()) {
            String sql = "BID_" + s + "%";
            String jsonStr = fixBugMapper.setSql(sql);
            aiTaskService.asyncAiTaskBatch(JSON.parseArray(jsonStr, AiTaskDTO.class));
        }

        log.info("[FixBugService][subsidyFixBug] -----  end  -----");
        return "你成功了！";
    }

}
