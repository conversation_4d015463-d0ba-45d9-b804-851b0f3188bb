package com.faw.work.ais.aic.model.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 质检方案表
 * <AUTHOR>
 */
@Data
@TableName("inspection_scheme")
public class InspectionSchemePO implements Serializable {

    @TableId("id")
    private Integer id;

    @TableField("name")
    private String name;

    @TableField("description")
    private String description;

    @TableField("status")
    private Integer status;

    @TableField("created_by")
    private String createdBy;

    @TableField("created_at")
    private Timestamp createdAt;

    @TableField("updated_by")
    private String updatedBy;

    @TableField("updated_at")
    private Timestamp updatedAt;
}

