package com.faw.work.ais.controller;

import com.dcp.common.rest.Result;
import com.faw.work.ais.entity.vo.ai.BizUnitInfoVO;
import com.faw.work.ais.entity.vo.ai.FlowInfoVO;
import com.faw.work.ais.entity.vo.ai.FlowVO;
import com.faw.work.ais.service.BupCoreService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

 /**
 * 角色工作台数据控制层
 * <AUTHOR>
 * @date 2025/05/09
 */

@Tag(name = "角色工作台数据控制层")
@Slf4j
@RestController("bupCoreController")
@RequestMapping("/eng")
public class BupCoreController {

    @Autowired
    BupCoreService bupCoreService;

    @Operation(summary = "流程下拉列表", description = "流程下拉列表")
    @PostMapping(value = "/getFlowList")
    public Result<FlowVO> getFlowList() {
        return Result.success(bupCoreService.getFlowList());
    }

    @Operation(summary = "业务单元列表", description = "业务单元列表")
    @PostMapping(value = "/getUnitList")
    public Result<List<BizUnitInfoVO>> getUnitList(@RequestBody FlowInfoVO req) {
        return Result.success(bupCoreService.getUnitList(req));
    }

}
