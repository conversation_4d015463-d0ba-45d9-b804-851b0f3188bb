package com.faw.work.ais.aic.service;


import com.faw.work.ais.aic.config.InspectionContext;
import com.faw.work.ais.aic.model.domain.InspectionRulePO;
import com.faw.work.ais.aic.model.dto.MatchedItemDTO;

import java.util.List;

/**
 * 质检规则检查器策略接口
 * <AUTHOR>
 */
public interface InspectionRuleChecker {

    /**
     * 对单个规则执行具体的检查逻辑
     * @param rule 当前待检查的规则
     * @param context 质检上下文，包含待检文本和预加载的规则项
     * @return 匹配到的质检项列表
     */
    List<MatchedItemDTO> check(InspectionRulePO rule, InspectionContext context);
}

