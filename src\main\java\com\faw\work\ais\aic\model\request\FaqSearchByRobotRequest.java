package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 根据机器人ID搜索FAQ请求对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "根据机器人ID搜索FAQ请求对象")
public class FaqSearchByRobotRequest {

    @Schema(description = "机器人ID")
    @NotNull(message = "机器人ID不能为空")
    private String robotId;

    @Schema(description = "查询内容")
    @NotBlank(message = "查询内容不能为空")
    private String query;

    @Schema(description = "返回结果数量")
    @NotNull(message = "返回结果数量不能为空")
    private Integer topK = 1;

    @Schema(description = "相似度阈值")
    @NotNull(message = "相似度阈值不能为空")
    private float similarityThreshold;

    @NotBlank(message = "环境不能为空")
    @Schema(description = "环境  test / prod")
    private String env;

    @Schema(description = "会话ID")
    private String sessionId;

    @Schema(description = "对话ID")
    private String chatId;

    public static FaqSearchByRobotRequest of(String robotId, String query, Integer topK, float similarityThreshold, String env, String sessionId, String chatId) {
        FaqSearchByRobotRequest request = new FaqSearchByRobotRequest();
        request.setRobotId(robotId);
        request.setQuery(query);
        request.setTopK(topK);
        request.setSimilarityThreshold(similarityThreshold);
        request.setEnv(env);
        request.setSessionId(sessionId);
        request.setChatId(chatId);
        return request;
    }
}