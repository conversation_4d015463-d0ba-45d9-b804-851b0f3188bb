package com.faw.work.ais.aic.model.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 质检方案规则关联表
 * <AUTHOR>
 */
@Data
@TableName("inspection_scheme_rule_joins")
public class InspectionSchemeRuleJoinsPO implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId("id")
    private Long id;

    @TableField("scheme_id")
    private Integer schemeId;

    @TableField("rule_id")
    private Integer ruleId;

    @TableField("priority")
    private Integer priority;

    @TableField("created_by")
    private String createdBy;

    @TableField("created_at")
    private Timestamp createdAt;

    @TableField("updated_by")
    private String updatedBy;

    @TableField("updated_at")
    private Timestamp updatedAt;
}

