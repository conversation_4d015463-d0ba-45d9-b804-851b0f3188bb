package com.faw.work.ais.aic.model.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 通话检查表
 * <AUTHOR>
 */
@Data
@TableName("inspection_item_voice")
public class InspectionItemVoicePO implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId("id")
    private Long id;

    @TableField("voice_type")
    private String voiceType;

    @TableField("voice_value")
    private String voiceValue;

    @TableField("relation")
    private String relation;

    @TableField("inspection_rule_id")
    private Long inspectionRuleId;

    @TableField("status")
    private Integer status;

    @TableField("created_by")
    private String createdBy;

    @TableField("created_at")
    private Timestamp createdAt;

    @TableField("updated_by")
    private String updatedBy;

    @TableField("updated_at")
    private Timestamp updatedAt;
}

