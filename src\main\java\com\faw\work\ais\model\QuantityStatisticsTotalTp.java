package com.faw.work.ais.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
* 数量统计表
* Created  by Mr.hp
* DateTime on 2025-01-13 14:12:35
 *     TP(8,"TP数：ai √ ，人工 √"),
 *     PF(9,"FP数：ai √ ，人工 ×"),
 *     TN(10,"TN数：ai × ，人工 ×"),
 *     FN(11,"FN数：ai × ，人工 √"),
* <AUTHOR> Mr.hp
*/
@Data
@NoArgsConstructor
@Schema(description = "QuantityStatisticsTotalTp", name = "人工复审材料准确率分布tp")
public class QuantityStatisticsTotalTp implements Serializable {

    private static final long serialVersionUID = 1L;



    /**
     * 系统ID
     * tpRate
     */
    @Schema(description = "tpRate")
    private BigDecimal tpRate;
    /**
     * 系统ID
     * tpNum
     */
    @Schema(description = "tpNumTP数：ai √ ，人工 √")
    private BigDecimal tpNum;

    /**
     * 系统ID
     * tpRate
     */
    @Schema(description = "tnRate")
    private BigDecimal tnRate;
    /**
     * 系统ID
     * tnNum
     */
    @Schema(description = "tnNumTN数：ai × ，人工 ×")
    private BigDecimal tnNum;

    /**
     * 系统ID
     * fpRate
     */
    @Schema(description = "fpRate")
    private BigDecimal fpRate;
    /**
     * 系统ID
     * fpNum
     */
    @Schema(description = "fpNumFP数：ai √ ，人工 ×")
    private BigDecimal fpNum;

    /**
     * 系统ID
     * fpRate
     */
    @Schema(description = "fnRate")
    private BigDecimal fnRate;
    /**
     * 系统ID
     * fpNum
     */
    @Schema(description = "fnNumFN数：ai × ，人工 √")
    private BigDecimal fnNum;




}
