package com.faw.work.ais.aic.service.strategy;


import cn.hutool.core.collection.CollUtil;
import com.faw.work.ais.aic.config.InspectionContext;
import com.faw.work.ais.aic.model.domain.InspectionItemNegativePO;
import com.faw.work.ais.aic.model.domain.InspectionRulePO;
import com.faw.work.ais.aic.model.dto.MatchedItemDTO;
import com.faw.work.ais.aic.service.InspectionRuleChecker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 负面词规则检查器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class NegativeRule<PERSON>hecker implements InspectionRuleChecker {

    private static final String TYPE = "negative";

    @Autowired
    private ChatClient aicChatClient;

    @Autowired
    @Qualifier("aiQualityInspectionExecutor")
    private Executor aiQualityInspectionExecutor;

    /**
     * {@inheritDoc}
     */
    @Override
    public List<MatchedItemDTO> check(InspectionRulePO rule, InspectionContext context) {
        List<InspectionItemNegativePO> negativeItems = context.getNegativeMap().get(rule.getId());
        String speech = context.getCustomerServiceSpeech();

        if (CollUtil.isEmpty(negativeItems)) {
            return Collections.emptyList();
        }

        // 1. 将每个质检项转换为一个异步任务 (CompletableFuture)
        List<CompletableFuture<MatchedItemDTO>> futures = negativeItems.stream()
                .map(item -> CompletableFuture.supplyAsync(() -> {
                    String systemPrompt = String.format(
                            "你是一个规则审核机器人。你的唯一任务是判断给定的文本是否违反了规则。" +
                                    "规则如下：%s" +
                                    "如果文本违反规则，你的回答必须且只能是单个数字 '1'。" +
                                    "如果文本未违反规则，你的回答必须且只能是单个数字 '0'。" +
                                    "不要输出任何其他解释、文字、标点或空格。",
                            item.getPrompt()
                    );

                    String content = aicChatClient.prompt()
                            .system(systemPrompt)
                            .options(ChatOptions.builder().model("qwen-turbo").temperature(0.01).build())
                            .user(speech)
                            .call()
                            .content();

                    boolean isMatched = content.contains("1");

                    return MatchedItemDTO.builder()
                            .type(TYPE)
                            .content(item.getPrompt())
                            .matched(isMatched)
                            .build();
                }, aiQualityInspectionExecutor).exceptionally(ex -> {
                    // 异常处理：如果某个AI调用失败，记录日志并返回一个“未匹配”的结果，避免整个流程失败
                    log.error("AI质检项失败, ruleId: {}, prompt: {}", rule.getId(), item.getPrompt(), ex);
                    return MatchedItemDTO.builder()
                            .type(TYPE)
                            .content(item.getPrompt())
                            .matched(false)
                            .build();
                })).toList();

        // 2. 等待所有异步任务完成，并收集结果
        return futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
    }
}

