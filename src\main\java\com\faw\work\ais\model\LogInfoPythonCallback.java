package com.faw.work.ais.model;

import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * log_info_python_callback_new
 * <AUTHOR>
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LogInfoPythonCallback implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * ai审核模型id
     */
    private Long auditModelSceneId;

    /**
     * 批次id
     */
    private String batchId;

    /**
     * 每次python请求的唯一id
     */
    private String traceId;

    /**
     * 调用python服务的时间
     */
    private Date pythonCallTime;

    /**
     * python服务结果返回时间
     */
    private Date pythonResultTime;

    /**
     * 回调时间
     */
    private Date callbackTime;

    /**
     * 日志更新时间
     */
    private Date updateTime;

    /**
     * 回调各系统收到返回结果时间
     */
    private Date callbackResultTime;

    /**
     * 文件的coskey
     */
    private String coskey;

    /**
     * 日志创建时间
     */
    private Date createTime;

    @Schema(description = "补推次数")
    private Integer pushCount;

    private static final long serialVersionUID = 1L;
}