package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.domain.MilvusRow;
import com.faw.work.ais.aic.model.dto.AutoEmbeddingDTO;
import com.faw.work.ais.aic.model.dto.MilvusField;
import com.faw.work.ais.aic.model.dto.MilvusSearchResult;
import com.faw.work.ais.aic.model.dto.VectorSearchResult;
import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.SearchResp;
import io.milvus.v2.service.vector.response.UpsertResp;

import java.util.List;

/**
 * Milvus向量数据库服务接口
 * <AUTHOR>
 */
public interface MilvusService {

    /**
     * 存储单个向量到Milvus。
     * @param collectionName 集合名称
     * @param vectorId 向量ID
     * @param properties 属性列表
     * @param embedding 向量数据
     * @return 插入结果
     */
    InsertResp saveEmbedding(String collectionName, String vectorId, List<MilvusField> properties, float[] embedding);

    /**
     * 批量保存向量到Milvus。
     * @param collectionName 集合名称
     * @param rows 向量数据列表
     * @return 插入结果
     */
    InsertResp saveEmbeddingBatch(String tenantKey,String collectionName, List<MilvusRow> rows);

    /**
     * 批量插入或更新向量。
     * @param collectionName 集合名称
     * @param rows 向量数据列表
     * @return Upsert结果
     */
    UpsertResp saveOrUpdateBatch(String tenantKey,String collectionName, List<MilvusRow> rows);

    /**
     * 自动向量化并批量保存到Milvus。
     * @param collectionName 集合名称
     * @param dataList 数据列表
     */
    void autoEmbeddingAndInsertMilvus(String collectionName, List<AutoEmbeddingDTO> dataList);


    /**
     * 根据向量查询相似内容。
     * @param collectionName 集合名称
     * @param embedding 查询向量
     * @param topK 返回结果数量
     * @param similarityThreshold 相似度阈值
     * @param filterString 过滤条件
     * @return 搜索结果列表
     */
    List<VectorSearchResult> searchByEmbedding(String collectionName, float[] embedding, int topK, float similarityThreshold, String filterString);

    /**
     * 根据向量查询相似内容，返回包含document_id和label的完整信息。
     *
     * @param tenantKey           租户密钥
     * @param collectionName      集合名称
     * @param embedding           查询向量
     * @param topK                返回结果数量
     * @param similarityThreshold 相似度阈值
     * @param filterString        过滤条件
     * @return 搜索结果列表
     */
    List<MilvusSearchResult> searchByEmbeddingWithDetails(String tenantKey,String collectionName, float[] embedding, int topK, float similarityThreshold, String filterString);

    /**
     * 根据ID列表删除向量。
     * @param collectionName 集合名称
     * @param ids ID列表
     * @param documentId 文档ID
     * @return 删除数量
     */
    long deleteByIds(String collectionName, List<String> ids, String documentId);

    /**
     * 根据ID列表删除向量。
     * @param collectionName 集合名称
     * @param ids ID列表
     * @return 删除数量
     */
    long deleteByIdsNew(String collectionName, List<String> ids);

    /**
     * 根据ID列表查询向量。
     * @param collectionName 集合名称
     * @param ids ID列表
     * @return 搜索结果列表
     */
    List<VectorSearchResult>  selectByIds(String collectionName, List<String> ids);

    /**
     * 根据ID列表查询MilvusRow对象。
     * @param collectionName 集合名称
     * @param ids ID列表
     * @return MilvusRow列表
     */
    List<MilvusRow> selectRowsByIds(String collectionName, List<String> ids);

    /**
     * 批量搜索向量。
     * @param collectionName 集合名称
     * @param embeddings 向量列表
     * @param topK 返回结果数量
     * @param filterString 过滤条件
     * @return 搜索结果列表
     */
    SearchResp batchSearch(String collectionName, List<List<Float>> embeddings, int topK, String filterString);

    /**
     * 强制刷盘并加载集合到内存
     * @param tenantKey Milvus 连接池key
     * @param collectionName 集合名称
     * @return 是否成功
     */
    boolean flushAndLoad(String tenantKey, String collectionName);

    /**
     * 按筛选器删除
     *
     * @param tenantKey      租户密钥
     * @param collectionName 集合名称
     * @param filter         滤器
     * @return long
     */
    long deleteByFilter(String tenantKey, String collectionName, String filter);
}
