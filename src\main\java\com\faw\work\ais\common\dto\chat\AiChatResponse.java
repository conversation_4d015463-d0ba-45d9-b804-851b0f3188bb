package com.faw.work.ais.common.dto.chat;

import com.faw.work.ais.common.enums.chat.ChatSceneEnum;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 智能聊天 响应参数类
 *
 * <AUTHOR>
 * @since 2025-05-30 10:03
 */
@Data
@Builder
public class AiChatResponse {

    /**
     * 聊天内容
     */
    private String content;

    /**
     * 答案来源标识（"0"-大模型、"1"-知识库）
     */
    private String answerSource;

    /**
     * 场景标识 {@link ChatSceneEnum}
     */
    private String sceneCode;

    /**
     * 结束原因
     */
    private String finishReason;

    /**
     * 参数
     */
    private Object params;

}
