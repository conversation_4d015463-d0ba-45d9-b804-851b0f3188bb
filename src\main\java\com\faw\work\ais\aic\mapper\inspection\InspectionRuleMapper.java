package com.faw.work.ais.aic.mapper.inspection;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.InspectionRulePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 质检规则表 Mapper 接口
 * <AUTHOR>
 */
@Mapper
public interface InspectionRuleMapper extends BaseMapper<InspectionRulePO> {

    /**
     * 根据质检方案ID查询所有启用的、并按优先级排序的规则
     * @param schemeId 质检方案ID
     * @return 规则列表
     */
    List<InspectionRulePO> selectRulesBySchemeId(@Param("schemeId") Long schemeId);
}
