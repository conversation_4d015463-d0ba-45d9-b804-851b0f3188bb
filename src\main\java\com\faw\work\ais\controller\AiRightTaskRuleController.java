package com.faw.work.ais.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.entity.dto.ai.AiKanBanDTO;
import com.faw.work.ais.entity.dto.ai.FileDTO;
import com.faw.work.ais.entity.dto.ai.NumberEmployeeDTO;
import com.faw.work.ais.entity.dto.ai.TaskRuleDTO;
import com.faw.work.ais.entity.vo.ai.*;
import com.faw.work.ais.model.TaskRule;
import com.faw.work.ais.service.KanBanService;
import com.faw.work.ais.service.RightTaskRuleService;
import com.faw.work.ais.service.TaskRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "AI看板")
@Slf4j
@RestController("AiRightController")
@RequestMapping("/rightTaskRule")
public class AiRightTaskRuleController {

    @Autowired
    private RightTaskRuleService rightTaskRuleService;
    @Operation(summary = "获取正确率", description = "获取正确率")
    @PostMapping(value = "/get")
    public Response<HumanSampleRightRateRuleVO> getSampleTaskRuleInfo(@RequestBody @Valid NumberEmployeeDTO numberEmployeeDTO) {
        HumanSampleRightRateRuleVO humanSampleRightRateRuleVO = rightTaskRuleService.getSampleTaskRuleInfo(numberEmployeeDTO);
        return Response.success(humanSampleRightRateRuleVO);
    }
}
