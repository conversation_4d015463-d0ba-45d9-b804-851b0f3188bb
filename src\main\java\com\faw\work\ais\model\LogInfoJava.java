package com.faw.work.ais.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import lombok.Data;

/**
 * log_info_java_new
 * <AUTHOR>
@Data
@ApiModel("log_info_java_new")
public class LogInfoJava implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 系统id
     */
    @ApiModelProperty("系统id")
    private String systemId;

    /**
     * 调用java服务的时间
     */
    @ApiModelProperty("调用java服务的时间")
    private Date javaCallTime;

    /**
     * 批次id
     */
    @ApiModelProperty("批次id")
    private String batchId;

    /**
     * 日志创建时间
     */
    @ApiModelProperty("日志创建时间")
    private Date createTime;

    /**
     * 日志更新时间
     */
    @ApiModelProperty("日志更新时间")
    private Date updateTime;

    /**
     * 调用java服务的系统入参
     */
    @ApiModelProperty("调用java服务的系统入参")
    private String javaParam;

    private static final long serialVersionUID = 1L;
}