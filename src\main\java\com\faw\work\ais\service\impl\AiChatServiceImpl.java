package com.faw.work.ais.service.impl;

import com.alibaba.dashscope.audio.asr.recognition.Recognition;
import com.alibaba.dashscope.audio.asr.recognition.RecognitionParam;
import com.alibaba.fastjson.JSON;
import com.dcp.common.rest.Result;
import com.faw.work.ais.aic.model.request.FaqSearchByRobotRequest;
import com.faw.work.ais.aic.model.response.FaqKnowledgeResponse;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.MessageConstants;
import com.faw.work.ais.common.PromptConstants;
import com.faw.work.ais.common.dto.chat.*;
import com.faw.work.ais.common.enums.ErrorCodeEnum;
import com.faw.work.ais.common.enums.chat.ChatSceneEnum;
import com.faw.work.ais.config.chat.ChatRedisMemory;
import com.faw.work.ais.mapper.chat.AppChatHistoryDetailMapper;
import com.faw.work.ais.mapper.chat.AppChatHistoryMapper;
import com.faw.work.ais.model.chat.AppChatHistory;
import com.faw.work.ais.model.chat.AppChatHistoryDetail;
import com.faw.work.ais.aic.service.FaqKnowledgeService;
import com.faw.work.ais.service.AiAgentService;
import com.faw.work.ais.service.AiChatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.*;

/**
 * 灵小犀模型 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-03 15:14
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AiChatServiceImpl implements AiChatService {

    private final AiAgentService aiAgentService;

    private final ChatClient universalClient;

    private final FaqKnowledgeService faqKnowledgeService;

    private final AppChatHistoryMapper appChatHistoryMapper;

    private final AppChatHistoryDetailMapper appChatHistoryDetailMapper;
    private final ChatRedisMemory chatRedisMemory;


    @Value("${spring.ai.dashscope.api-key}")
    private String apiKey;

    @Value("${spring.ai.asr.model:}")
    private String asrModel;

    @Value("${spring.ai.asr.format:}")
    private String asrFormat;

    @Value("${spring.ai.asr.sample-rate:10}")
    private int asrSampleRate;

    @Value("${spring.ai.chat-intention.robot-id:}")
    private String robotId;

    @Value("${spring.ai.chat-intention.top-k:}")
    private Integer topK;

    @Value("${spring.ai.chat-intention.env:}")
    private String env;

    @Value("${spring.ai.chat-intention.threshold:}")
    private Float similarityThreshold;


    @Override
    public Flux<AiChatResponse> continuousSession(AiChatRequest request) {
        log.info("[AiChatService][continuousSession][entrance] request: {}", JSON.toJSONString(request));
        // 1 check params
        if (Objects.isNull(request) || StringUtils.isEmpty(request.getAppId()) || Objects.isNull(request.getUserInfo())
                || StringUtils.isEmpty(request.getSessionId()) || StringUtils.isEmpty(request.getQuestion())) {
            return Flux.just(AiChatResponse.builder().content(ErrorCodeEnum.REQUEST_PARAM_NULL.getDesc()).build());
        }

        // 2 init params
        String cacheId = UUID.randomUUID().toString();
        String chatId = String.join(
                CommonConstants.COLON, request.getAppId(), request.getUserInfo().getUserCode(), request.getSessionId());
        AiAgentRequest agentRequest = AiAgentRequest.builder().sceneCode(ChatSceneEnum.SMALL_TALK.getCode())
                .userInfo(request.getUserInfo()).cacheId(cacheId).chatId(chatId).question(request.getQuestion()).build();

        // 3 identify intent
        this.identifyIntent(request, agentRequest);

        // 4 to chat
        Flux<AiChatResponse> result = aiAgentService.getAgentFluxResponse(agentRequest);

        log.info("[AiChatService][continuousSession][exit] text: {}", result);
        return result;
    }

    @Override
    public Result<VoiceToTextEntity> voiceToText(MultipartFile file) {
        Recognition recognizer = new Recognition();
        RecognitionParam param =
                RecognitionParam.builder().apiKey(apiKey).model(asrModel).format(asrFormat).sampleRate(asrSampleRate)
                        .parameter(CommonConstants.VOICE_LANGUAGE_HINTS, new String[]{"zh", "en"})
                        .build();

        String fileName = UUID.randomUUID() + CommonConstants.FILE_SUFFIX_VOICE;
        File voiceFile = null;
        VoiceToTextEntity result = null;
        try {
            voiceFile = new File(fileName);
            this.fileConvert(file, voiceFile);
            String response = recognizer.call(param, voiceFile);

            if (StringUtils.isNotEmpty(response)) {
                result = JSON.parseObject(response, VoiceToTextEntity.class);
            }
        } catch (Exception e) {
            log.warn("[AiChatService][voiceToText] exception: ", e);
            return Result.failed(e.getMessage());
        } finally {
            if (Objects.nonNull(voiceFile)) {
                boolean deleteFlag = voiceFile.delete();
                log.info("[AiChatService][voiceToText] deleteFlag: {}", deleteFlag);
            }
        }

        return Result.success(result);
    }

    @Override
    public String voiceToTextRectify(MultipartFile file) {
        // 1 voice to text
        Result<VoiceToTextEntity> textResult = this.voiceToText(file);
        if (!textResult.isSuccess() || CollectionUtils.isEmpty(textResult.getData().getSentences())) {
            return MessageConstants.VOICE_TO_TEXT_ERROR;
        }

        // 2 rectify text
        String rectifyResult = universalClient.prompt(PromptConstants.RECTIFY_TEXT_PROMPT)
                .user(textResult.getData().getSentences().get(CommonConstants.FIRST_INDEX).getText())
                .call().content();

        log.info("[AiChatService][voiceToTextRectify][exit] rectifyResult: {}", rectifyResult);
        return rectifyResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<AppChatHistoryDto> saveSessionRecord(AppChatHistoryDto request) {
        log.info("[AiChatService][saveSessionRecord][entrance] request: {}", JSON.toJSONString(request));
        // 1 check params
        if (Objects.isNull(request) || CollectionUtils.isEmpty(request.getDetailList())) {
            return Result.failed(ErrorCodeEnum.REQUEST_PARAM_NULL.getDesc());
        }

        // 2 save AppChatHistory
        if (Objects.isNull(request.getId())) {
            AppChatHistory appChatHistory = new AppChatHistory();
            BeanUtils.copyProperties(request, appChatHistory);
            appChatHistory.setDelFlag(CommonConstants.FALSE_INT);

            int historyRows = appChatHistoryMapper.insert(appChatHistory);
            log.info("[AiChatService][saveSessionRecord] historyRows: {}", historyRows);

            request.setId(appChatHistory.getId());
        }

        // 3 save AppChatHistoryDetail
        int deleteRows = appChatHistoryDetailMapper.deleteByChatId(request.getId());
        log.info("[AiChatService][saveSessionRecord] deleteRows: {}", deleteRows);

        request.getDetailList().forEach(detail -> detail.setChatId(request.getId()));
        int detailRows = appChatHistoryDetailMapper.insertList(request.getDetailList());
        log.info("[AiChatService][saveSessionRecord] detailRows: {}", detailRows);

        return Result.success(AppChatHistoryDto.builder().id(request.getId()).build());
    }

    @Override
    public Result<List<AppChatHistoryDto>> querySessionRecord(AppChatHistoryDto request) {
        log.info("[AiChatService][querySessionRecord][entrance] request: {}", JSON.toJSONString(request));
        // 1 check params
        if (Objects.isNull(request)
                || StringUtils.isEmpty(request.getAppId()) || StringUtils.isEmpty(request.getUserId())) {
            return Result.failed(ErrorCodeEnum.REQUEST_PARAM_NULL.getDesc());
        }

        AppChatHistory appChatHistory = new AppChatHistory();
        BeanUtils.copyProperties(request, appChatHistory);
        appChatHistory.setDelFlag(CommonConstants.FALSE_INT);
        List<AppChatHistory> appChatHistoryList = appChatHistoryMapper.selectList(appChatHistory);

        return Result.success(appChatHistoryList.stream().map(this::convertPoToDto).toList());
    }

    @Override
    public Result<List<AppChatHistoryDetailDto>> querySessionDetail(AppChatHistoryDto request) {
        log.info("[AiChatService][querySessionDetail][entrance] request: {}", JSON.toJSONString(request));
        // 1 check params
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            return Result.failed(ErrorCodeEnum.REQUEST_PARAM_NULL.getDesc());
        }

        AppChatHistoryDetail appChatHistoryDetail = new AppChatHistoryDetail();
        appChatHistoryDetail.setChatId(request.getId());
        List<AppChatHistoryDetail> detailList = appChatHistoryDetailMapper.selectList(appChatHistoryDetail);

        return Result.success(detailList.stream().map(this::convertPoToDto).toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> deleteSessionRecord(AppChatHistoryDto request) {
        log.info("[AiChatService][deleteSessionRecord][entrance] request: {}", JSON.toJSONString(request));
        // 1 check params
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            return Result.failed(ErrorCodeEnum.REQUEST_PARAM_NULL.getDesc());
        }

        AppChatHistory appChatHistory = new AppChatHistory();
        appChatHistory.setId(request.getId());
        appChatHistory.setDelFlag(CommonConstants.TRUE_INT);

        int deleteRows = appChatHistoryMapper.update(appChatHistory);
        log.info("[AiChatService][deleteSessionRecord] deleteRows: {}", deleteRows);

        return Result.success(CommonConstants.TRUE);
    }

    /**
     * 识别意图（聊天前处理）
     *
     * @param chatRequest 用户问题
     * @param agentRequest 智能体请求参数
     */
    private void identifyIntent(AiChatRequest chatRequest, AiAgentRequest agentRequest) {
        log.info("[AiChatService][identifyIntent][entrance] chatRequest: {}, agentRequest: {}", JSON.toJSONString(chatRequest), JSON.toJSONString(agentRequest));

        // 1 意图识别 FAQ+Agent
        FaqSearchByRobotRequest faqRequest = FaqSearchByRobotRequest.of(robotId, chatRequest.getQuestion(), topK, similarityThreshold, env, null, null);
        List<FaqKnowledgeResponse> faqList = faqKnowledgeService.searchByRobotId(faqRequest);
        if (!CollectionUtils.isEmpty(faqList)) {
            agentRequest.setSceneCode(faqList.get(CommonConstants.FIRST_INDEX).getAnswer());
        } else {
            String question = chatRequest.getQuestion();

            // 获取上一轮问题与这轮问题一起送入意图识别智能体
            List<Message> messages = chatRedisMemory.get(agentRequest.getChatId(), CommonConstants.TWO);
            if (messages != null && messages.size() >= CommonConstants.TWO) {
                question = String.format(PromptConstants.IDENTIFY_INTENT_QUESTION_PROMPT,
                        messages.get(0).getText(), messages.get(1).getText(), chatRequest.getQuestion());
            }

            AiAgentRequest intentAgentRequest = AiAgentRequest.builder()
                    .sceneCode(ChatSceneEnum.IDENTIFY_INTENT.getCode()).question(question).build();
            String intentResult = aiAgentService.getAgentResponse(intentAgentRequest);

            agentRequest.setSceneCode(intentResult);
        }

        // 2 图片识别
        if (!CollectionUtils.isEmpty(chatRequest.getImageUrls())) {
            AiAgentRequest photoAgentRequest = AiAgentRequest.builder().sceneCode(ChatSceneEnum.ANALYZE_PHOTO.getCode())
                    .imageUrls(chatRequest.getImageUrls()).question(chatRequest.getQuestion()).build();

            String analyzeResult = aiAgentService.getAgentResponse(photoAgentRequest);

            agentRequest.setQuestion(String.format(PromptConstants.USER_QUESTION_PROMPT, chatRequest.getQuestion(), analyzeResult));
        }
    }

    /**
     * 文件转换
     *
     * @param multipartFile 文件
     * @param file          文件
     */
    private void fileConvert(MultipartFile multipartFile, File file) {
        if (Objects.isNull(multipartFile) || Objects.isNull(file)) {
            return;
        }

        try (InputStream inputStream = multipartFile.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(file)) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

        } catch (Exception e) {
            log.warn("[AiChatService][fileConvert] exception: ", e);
        }
    }

    /**
     * AppChatHistory po 转 dto
     *
     * @param po po
     * @return dto
     */
    private AppChatHistoryDto convertPoToDto(AppChatHistory po) {
        if (Objects.isNull(po)) {
            return null;
        }

        AppChatHistoryDto dto = new AppChatHistoryDto();
        BeanUtils.copyProperties(po, dto);

        return dto;
    }

    /**
     * AppChatHistoryDetail po 转 dto
     *
     * @param po po
     * @return dto
     */
    private AppChatHistoryDetailDto convertPoToDto(AppChatHistoryDetail po) {
        if (Objects.isNull(po)) {
            return null;
        }

        AppChatHistoryDetailDto dto = new AppChatHistoryDetailDto();
        BeanUtils.copyProperties(po, dto);

        return dto;
    }

}
