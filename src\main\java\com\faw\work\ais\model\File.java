package com.faw.work.ais.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigInteger;
import java.time.LocalDateTime;

/**
 * 文件信息
 */
@Data
@TableName(value = "file")
@Accessors(chain = true)
public class File {

    @Schema(description = "cos的key")
    private String id;

    @Schema(description = "文件大小b")
    private String length;

    @Schema(description = "md5值")
    private String md5;

    @Schema(description = "上传时间")
    private LocalDateTime createTime;

    @Schema(description = "文件原始名称")
    private String rawName;

    @Schema(description = "文件的序号")
    private Integer fileIndex;

    @Schema(description = "文件的描述")
    private String fileDesc;

    @Schema(description = "文件的版本号")
    private String version;
}
