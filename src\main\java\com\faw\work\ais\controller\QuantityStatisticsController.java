package com.faw.work.ais.controller;

import com.dcp.common.rest.Result;
import com.faw.work.ais.model.*;
import com.faw.work.ais.service.QuantityStatisticsService;
import lombok.extern.slf4j.Slf4j;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.*;

/**
* 数量统计表
* Created  by Mr.hp
* <AUTHOR> Mr.hp
* DateTime on 2025-01-13 14:12:35
*/
@Tag(name = "数量统计表")
@RestController
@Slf4j
public class QuantityStatisticsController {

    @Autowired
    private QuantityStatisticsService quantityStatisticsService;

    /**
    * 新增或修改数量统计表
    */
    @Operation(summary = "新增或修改数量统计表-job每天3.15 执行刷新前一天数据进 结果表")
    @RequestMapping(value="/quantityStatistics/insertOrUpdateJob" , method = RequestMethod.POST)
    public Result<Integer> insertOrUpdate(@RequestBody QuantityStatistics quantityStatistics){
        return   quantityStatisticsService.insertOrUpdate(quantityStatistics);

    }

    /**
    * 新增数量统计表
    */
    @Operation(summary = "新增数量统计表-刷新歷史記錄")
    @RequestMapping(value="/quantityStatistics/insertHis" , method = RequestMethod.POST)
    public Result<Integer> insert(@RequestBody QuantityStatistics quantityStatistics){
    return quantityStatisticsService.insert(quantityStatistics);

    }


    /**
    * 分页查询数量统计表
    */
    @Operation(summary = "分页查询数量统计表")
    @RequestMapping(value="/quantityStatistics/getQuantityStatisticsList" , method = RequestMethod.POST)
    public Result<List<QuantityStatisticsDayValueRes>> getQuantityStatisticsList(@RequestBody QuantityStatistics quantityStatistics){
    return    quantityStatisticsService.getQuantityStatisticsList(quantityStatistics);

   }

    /**
     * 分页查询数量统计表
     */
    @Operation(summary = "AI审核任务数")
    @RequestMapping(value="/quantityStatistics/getAiQuantityStatistics" , method = RequestMethod.POST)
    public Result<QuantityStatisticsTotalValue> getAiQuantityStatistics(@RequestBody QuantityStatistics quantityStatistics){
        return    quantityStatisticsService.getAiQuantityStatistics(quantityStatistics);

    }
    /**
     * 分页查询数量统计表
     */
    @Operation(summary = "人工审核准确数")
    @RequestMapping(value="/quantityStatistics/getHumQuantityStatistics" , method = RequestMethod.POST)
    public Result<QuantityStatisticsTotalValue> getHumQuantityStatistics(@RequestBody QuantityStatistics quantityStatistics){
        return    quantityStatisticsService.getHumQuantityStatistics(quantityStatistics);

    }
    /**
     * 分页查询数量统计表
     */
    @Operation(summary = "人工审核tp,fp准确数")
    @RequestMapping(value="/quantityStatistics/getHumTpStatistics" , method = RequestMethod.POST)
    public Result<QuantityStatisticsTotalTp> getHumTpStatistics(@RequestBody QuantityStatistics quantityStatistics){
        return    quantityStatisticsService.getHumTpStatistics(quantityStatistics);

    }


}

