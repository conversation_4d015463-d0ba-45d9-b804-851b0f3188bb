<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.inspection.InspectionRuleMapper">

    <select id="selectRulesBySchemeId" resultType="com.faw.work.ais.aic.model.domain.InspectionRulePO">
        SELECT
            ir.id,
            ir.rule_name,
            ir.rule_condition,
            ir.rule_type,
            ir.score,
            ir.status,
            ir.description
        FROM
            inspection_rule ir
                JOIN
            inspection_scheme_rule_joins isrj ON ir.id = isrj.rule_id
        WHERE
            isrj.scheme_id = #{schemeId}
          AND ir.status = 1
        ORDER BY
            isrj.priority ASC
    </select>

</mapper>
