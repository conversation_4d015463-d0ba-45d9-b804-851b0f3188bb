package com.faw.work.ais.model.base;



import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:分页结果
 * @author: wa<PERSON><PERSON><PERSON>
 * @date: 2022年08月31日 12:40
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(name= "PageResult")
public class PageResult<T> {

    @Schema(name= "总数")
    private Long total;

    @Schema(name= "每页数量")
    private Integer pageSize;

    @Schema(name= "页码")
    private Integer pageNum;

    @Schema(name= "数据")
    private List<T> data;

}
