package com.faw.work.ais.aic.model.response;

import com.faw.work.ais.aic.model.domain.FaqCategoryPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * FAQ机器人响应对象
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "FAQ机器人响应对象")
public class FaqRobotResponse {
    
    @Schema(description = "机器人ID")
    private String id;
    
    @Schema(description = "机器人名称")
    private String robotName;
    
    @Schema(description = "机器人描述")
    private String description;
    
    @Schema(description = "发布状态(0未发布 1 已发布）")
    private Integer status;
    
    @Schema(description = "创建人")
    private String createdBy;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新人")
    private String updatedBy;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "类目列表")
    private List<FaqCategoryPO> categoryList;
} 