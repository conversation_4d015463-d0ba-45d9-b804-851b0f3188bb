package com.faw.work.ais.aic.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.cloud.ai.model.RerankModel;
import com.alibaba.cloud.ai.model.RerankRequest;
import com.alibaba.cloud.ai.model.RerankResponse;
import com.alibaba.fastjson2.JSON;
import com.faw.work.ais.aic.common.enums.EmbeddingModelTypeEnum;
import com.faw.work.ais.aic.model.dto.EmbeddingPropertyDTO;
import com.faw.work.ais.aic.model.request.EmbeddingRequest;
import com.faw.work.ais.aic.model.response.EmbeddingResponse;
import com.faw.work.ais.aic.service.EmbeddingService;
import com.faw.work.ais.common.enums.Constants;
import com.faw.work.ais.common.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.core5.util.Timeout;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EmbeddingServiceImpl implements EmbeddingService {

    @Autowired
    private RerankModel rerankModel;
    @Autowired
    private Executor embeddingThreadPool;
    @Value("${ali.embedding.api-key:}")
    private String apiKey;

    @Value("${ali.embedding.url:https://dashscope.aliyuncs.com/compatible-mode/v1/embeddings}")
    private String embeddingUrl;


    private final EmbeddingModel embeddingModelV3;
    private final EmbeddingModel embeddingModelV1;
    private final EmbeddingModel embeddingModelV2;

    @Qualifier("textEmbeddingV1")
    private final EmbeddingModel textEmbeddingV1;

    @Qualifier("textEmbeddingV2")
    private final EmbeddingModel textEmbeddingV2;

    @Qualifier("textEmbeddingV3")
    private final EmbeddingModel textEmbeddingV3;

    private static final CloseableHttpClient HTTP_CLIENT = HttpClients.custom()
            .setDefaultRequestConfig(RequestConfig.custom()
                    .setConnectionRequestTimeout(Timeout.ofSeconds(10))
                    .setResponseTimeout(Timeout.ofSeconds(30))
                    .build())
            .build();

    @Override
    public float[] getEmbeddingWithHttp(String text, String model) {
        // 构建请求实体
        EmbeddingRequest requestEntity = EmbeddingRequest.builder()
                .model(model)
                .input(text)
                .encoding_format("float")
                .build();

        String requestId = UUID.randomUUID().toString();
        String requestJson = JSON.toJSONString(requestEntity);

        HttpPost httpPost = new HttpPost(embeddingUrl);
        httpPost.setHeader("Authorization", "Bearer " + apiKey);
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("X-DashScope-RequestId", requestId);
        httpPost.setEntity(new StringEntity(requestJson, ContentType.APPLICATION_JSON));

        try {
            return HTTP_CLIENT.execute(httpPost, response -> {
                int statusCode = response.getCode();

                if (statusCode != Constants.SUCCESS) {
                    throw new BizException("获取文本向量失败");
                }

                if (response.getEntity() == null) {
                    throw new BizException("响应体为空");
                }

                String responseString = EntityUtils.toString(response.getEntity());
                log.debug("阿里云文本嵌入模型响应: {}", responseString);

                // 解析响应实体
                EmbeddingResponse responseEntity = JSON.parseObject(responseString, EmbeddingResponse.class);
                if (responseEntity.getData() == null || responseEntity.getData().isEmpty() ||
                        responseEntity.getData().get(0).getEmbedding() == null) {
                    throw new BizException("响应格式错误，未找到embedding字段");
                }

                float[] embedding = responseEntity.getData().get(0).getEmbedding();

                if (embedding.length == 0) {
                    throw new BizException("未获取到文本向量");
                }

                return embedding;
            });
        } catch (IOException e) {
            throw new BizException(e.getMessage());
        }
    }

    @Override
    public float[] getEmbedding(String text) {
        return embeddingModelV3.embed(text);
    }
    @Override
    public float[] getEmbeddingV1(String text) {
        return textEmbeddingV1.embed(text);
    }
    @Override
    public float[] getEmbeddingV2(String text) {
        return textEmbeddingV2.embed(text);
    }
    @Override
    public float[] getEmbeddingV3(String text) {
        return textEmbeddingV3.embed(text);
    }

    @Override
    public float[] getEmbeddingByModel(EmbeddingModelTypeEnum modelType, String text) {
        if (modelType == null){
            throw new BizException("模型类型不能为空");
        }
        if (modelType == EmbeddingModelTypeEnum.TEXT_EMBEDDING_V1){
            return embeddingModelV1.embed(text);
        }
        if (modelType == EmbeddingModelTypeEnum.TEXT_EMBEDDING_V2){
            return embeddingModelV2.embed(text);
        }
        if (modelType == EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3){
            return embeddingModelV3.embed(text);
        }
        throw new BizException("不支持的模型类型");
    }


    @Override
    public List<EmbeddingPropertyDTO> getEmbeddingList(EmbeddingModelTypeEnum modelType,List<String> textList) {

        EmbeddingModel embeddingModel = null;
        int size = 10;
        if (modelType == null){
            throw new BizException("模型类型不能为空");
        }
        if (modelType == EmbeddingModelTypeEnum.TEXT_EMBEDDING_V1){
             embeddingModel = embeddingModelV1;
             size=25;
        }
        if (modelType == EmbeddingModelTypeEnum.TEXT_EMBEDDING_V2){
            embeddingModel = embeddingModelV2;
            size=25;
        }
        if (modelType == EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3){
            embeddingModel = embeddingModelV3;
        }

        // 按照模型要求切分批次（每批不超过10条）

        List<List<String>> batches = CollUtil.split(textList, size);

        // 创建并行任务流（保持顺序的线程安全集合）
        EmbeddingModel finalEmbeddingModel = embeddingModel;
        List<CompletableFuture<List<EmbeddingPropertyDTO>>> futures = batches.stream()
                .map(batch -> CompletableFuture.supplyAsync(
                        () -> processBatch(batch, finalEmbeddingModel),
                        embeddingThreadPool
                )).toList();

        // 合并所有批次结果
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .flatMap(future -> {
                            try {
                                return future.get().stream();
                            } catch (Exception e) {
                                throw new CompletionException("Batch processing failed", e);
                            }
                        })
                        .collect(Collectors.toList()))
                .join();
    }

    @Override
    public RerankResponse getReRankResult(RerankRequest request) {
        return rerankModel.call(request);
    }

    private List<EmbeddingPropertyDTO> processBatch(List<String> batch,EmbeddingModel embeddingModel) {

        List<float[]> embeddings = embeddingModel.embed(batch);
        List<EmbeddingPropertyDTO> batchResult = new ArrayList<>(batch.size());

        for (int i = 0; i < batch.size(); i++) {
            batchResult.add(EmbeddingPropertyDTO.builder()
                    .text(batch.get(i))
                    .embedding(embeddings.get(i))
                    .build());
        }
        return batchResult;
    }
}