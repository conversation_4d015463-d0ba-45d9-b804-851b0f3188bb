package com.faw.work.ais.controller;

import com.dcp.common.rest.Result;
import com.faw.work.ais.model.AiAuditModel;
import com.faw.work.ais.model.AiTechnologyModel;
import com.faw.work.ais.model.base.PageList;
import com.faw.work.ais.service.AiAuditModelService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;


import org.springframework.web.bind.annotation.*;

/**
 * ai审核模型配置表
 * Created  by Mr.hp
 *
 * <AUTHOR> Mr.hp
 * DateTime on 2025-05-08 10:36:35
 */
//@Api(description = "ai审核模型配置表")
@Schema(name = "人工智能ai审核模型配置表")
@RestController
@Slf4j
public class AiAuditModelController {

    @Autowired
    private AiAuditModelService aiAuditModelService;

    /**
     * 新增或修改ai审核模型配置表
     */
    //@ApiOperation(value = "新增或修改ai审核模型配置表",notes = "新增或修改ai审核模型配置表")
    @Operation(summary = "新增或修改ai审核模型配置表", description = "[author:10120165]")
    @RequestMapping(value = "/aiAuditModel/insertOrUpdate", method = RequestMethod.POST)
    public Result<Integer> insertOrUpdate(@RequestBody AiAuditModel aiAuditModel) {

        return aiAuditModelService.insertOrUpdate(aiAuditModel);

    }

    /**
     * 新增ai审核模型配置表
     */
    //@ApiOperation(value = "新增ai审核模型配置表",notes = "新增ai审核模型配置表")
    @Operation(summary = "新增ai审核模型配置表", description = "[author:10120165]")
    @RequestMapping(value = "/aiAuditModel/insert", method = RequestMethod.POST)
    public Result<Integer> insert(@RequestBody AiAuditModel aiAuditModel) {
        return aiAuditModelService.insert(aiAuditModel);

    }


    /**
     * 修改ai审核模型配置表
     */
    //@ApiOperation(value = "修改ai审核模型配置表",notes = "修改ai审核模型配置表")
    @Operation(summary = "修改ai审核模型配置表", description = "[author:10120165]")
    @RequestMapping(value = "/aiAuditModel/update", method = RequestMethod.POST)
    public Result<Integer> update(@RequestBody AiAuditModel aiAuditModel) {
        return aiAuditModelService.update(aiAuditModel);

    }

    /**
     * 根据Id查询ai审核模型配置表
     */
    //@ApiOperation(value = "根据Id查询ai审核模型配置表",notes = "根据Id查询ai审核模型配置表")
    @Operation(summary = "根据Id查询ai审核模型配置表", description = "[author:10120165]")
    @RequestMapping(value = "/aiAuditModel/getAiAuditModelById", method = RequestMethod.POST)
    public Result<AiAuditModel> getAiAuditModelById(@RequestBody AiAuditModel aiAuditModel) {
        return aiAuditModelService.getAiAuditModelById(aiAuditModel.getId());

    }

    /**
     * 分页查询ai审核模型配置表
     */
    //@ApiOperation(value = "分页查询ai审核模型配置表",notes = "分页查询ai审核模型配置表")
    @Operation(summary = "分页查询ai审核模型配置表", description = "[author:10120165]")
    @RequestMapping(value = "/aiAuditModel/getAiAuditModelList", method = RequestMethod.POST)
    public Result<PageList<AiAuditModel>> getAiAuditModelList(@RequestBody AiAuditModel aiAuditModel) {
        return aiAuditModelService.getAiAuditModelList(aiAuditModel);

    }

    /**
     * 分页查询ai技术模型配置表
     */
    //@ApiOperation(value = "分页查询ai技术模型配置表",notes = "分页查询ai技术模型配置表")
    @Operation(summary = "分页查询ai技术模型配置表", description = "[author:10120165]")
    @RequestMapping(value = "/aiAuditModel/getAiTechnologyModelList", method = RequestMethod.POST)
    public Result<List<AiTechnologyModel>> getAiTechnologyModelList(@RequestBody AiTechnologyModel aiTechnologyModel) {
        return aiAuditModelService.getAiTechnologyModelList(aiTechnologyModel);

    }


}

