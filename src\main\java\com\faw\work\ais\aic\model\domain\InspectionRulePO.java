package com.faw.work.ais.aic.model.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 质检规则表
 * <AUTHOR>
 */
@Data
@TableName("inspection_rule")
public class InspectionRulePO implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId("id")
    private Integer id;

    @TableField("rule_name")
    private String ruleName;

    @TableField("rule_condition")
    private String ruleCondition;

    @TableField("rule_type")
    private String ruleType;

    @TableField("score")
    private Integer score;

    @TableField("status")
    private Integer status;

    @TableField("description")
    private String description;

    @TableField("created_by")
    private String createdBy;

    @TableField("created_at")
    private Timestamp createdAt;

    @TableField("updated_by")
    private String updatedBy;

    @TableField("updated_at")
    private Timestamp updatedAt;
}

