package com.faw.work.ais.service.tool;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.aic.common.util.RedisService;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.dto.chat.ToolCacheEntity;
import com.faw.work.ais.common.enums.chat.ChatToolEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;

/**
 * 功能跳转服务类，用于实现功能跳转
 *
 * <AUTHOR>
 * @since 2025-06-24 11:13
 */
@Slf4j
public class FunctionService {

    private final RedisService redisService;


    public FunctionService(RedisService redisService) {
        this.redisService = redisService;
    }


    private static final String STAFF_SERVICE_MESSAGE = """
            请点击下方入口，直达在线客服；
            或者拨打人工坐席电话：400-81717
            """;


    @Tool(name = "staffService", description = "在线客服方法：当用户提及人工客服、在线客服时执行")
    public String staffService(ToolContext toolContext) {
        log.info("[FunctionService][staffService][entrance] toolContext: {}", JSON.toJSONString(toolContext));

        ToolCacheEntity toolCache = ToolCacheEntity.builder().toolName(ChatToolEnum.STAFF_SERVICE.getName()).toolStatus(true).build();
        redisService.set((String) toolContext.getContext().get(CommonConstants.KEY_CACHE_ID), JSON.toJSONString(toolCache), CommonConstants.SECONDS_ONE_HOUR);

        return STAFF_SERVICE_MESSAGE;
    }
}
