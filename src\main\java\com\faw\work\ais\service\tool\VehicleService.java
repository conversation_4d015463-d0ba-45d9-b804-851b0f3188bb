package com.faw.work.ais.service.tool;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.aic.common.util.RedisService;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.dto.chat.*;
import com.faw.work.ais.common.enums.chat.ChatToolEnum;
import com.faw.work.ais.feign.chat.AppFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;

import java.util.List;
import java.util.Objects;

/**
 * 车辆服务类，用于处理车辆相关的业务逻辑
 *
 * <AUTHOR>
 * @since 2025-05-29 15:54
 */
@Slf4j
public class VehicleService {

    private final RedisService redisService;

    private final AppFeignClient appFeignClient;


    public VehicleService(RedisService redisService, AppFeignClient appFeignClient) {
        this.redisService = redisService;
        this.appFeignClient = appFeignClient;
    }


    private static final String PICK_UP_CAR_SUCCESS_MESSAGE = """
            请点击下方入口，直达上门取车预约页面。
            """;

    private static final String APP_SUCCESS_FLAG = "000000";

    private static final String MAINTENANCE_ERROR_MESSAGE = "预约失败，请联系系统管理员";

    private static final String MAINTENANCE_SUCCESS_MESSAGE = """
            「好的，我来帮您安排一次维保服务」
            
            #### 根据系统识别您的用车信息：
            - **车辆：**红旗 %s（车牌：%s）
            - **最近服务门店：**%s（上次服务时间%s）
            - **推荐门店：**%s
            - **推荐时间：**%s（剩余%s个工位）
            
            #### 推荐理由：
            - 系统检测您上次也是在该门店保养，服务记录良好
            - 当前该店%s有空余工位，支持快速预约
            
            #### 请确认以下信息，点击一键预约也可修改后再提交：
            - **预约项目：**保养
            - **门店：**%s
            - **预约时间：**%s
            - **车型：**红旗 %s
            - **联系方式：**%s
            """;

    private static final String MAINTENANCE_FAIL_MESSAGE = """
            「抱歉，我无法帮您预约维修保养服务」
            
            #### 失败原因如下：
            %s
            """;


    @Tool(name = "pickUpCar", description = "预约上门取车方法：帮助用户预约预约上门取车服务")
    public String pickUpCar(ToolContext toolContext) {
        log.info("[VehicleService][pickUpCar][entrance] toolContext: {}", JSON.toJSONString(toolContext));

        ToolCacheEntity toolCache = ToolCacheEntity.builder().toolName(ChatToolEnum.PICK_UP_CAR.getName()).toolStatus(true).build();
        redisService.set((String) toolContext.getContext().get(CommonConstants.KEY_CACHE_ID), JSON.toJSONString(toolCache), CommonConstants.SECONDS_ONE_HOUR);

        return PICK_UP_CAR_SUCCESS_MESSAGE;
    }

    @Tool(name = "maintenance", returnDirect = true, description = "预约维修保养方法：帮助用户预约维修保养服务")
    public String maintenance(ToolContext toolContext) {
        log.info("[VehicleService][maintenance][entrance] toolContext: {}", JSON.toJSONString(toolContext));

        // 1 send request
        UserInfo userInfo = (UserInfo) toolContext.getContext().get(CommonConstants.KEY_USER_INFO);
        AppResponse<MaintenanceEntity> appResponse = appFeignClient.orderMaintenance(
                AppRequest.builder().aid(userInfo.getUserCode()).cityCode(userInfo.getCityCode())
                        .lat(userInfo.getLat()).lon(userInfo.getLon()).vin(userInfo.getVin()).build());
        log.info("[VehicleService][maintenance] appResponse: {}", JSON.toJSONString(appResponse));

        // 2 deal with response
        String message;
        ToolCacheEntity toolCache = ToolCacheEntity.builder().toolName(ChatToolEnum.MAINTENANCE.getName()).toolStatus(false).build();
        if (Objects.isNull(appResponse)) {
            message = MAINTENANCE_ERROR_MESSAGE;
        } else if (APP_SUCCESS_FLAG.equals(appResponse.getCode())) {
            toolCache.setToolStatus(true);
            toolCache.setToolValue(appResponse.getData());

            MaintenanceEntity data = appResponse.getData();
            String appointmentTime = String.join(CommonConstants.BLANK, data.getAppointmentDate(), data.getAppointmentDateTime());

            message = String.format(MAINTENANCE_SUCCESS_MESSAGE, data.getCarSeries(), data.getVehicleNumber(),
                    data.getLastEnterDealerName(), data.getLastEnterTime2(), data.getDealerName(), appointmentTime,
                    data.getRepairTimes(), appointmentTime, data.getDealerName(), appointmentTime, data.getCarSeries(), userInfo.getPhone());
        } else {
            message = String.format(MAINTENANCE_FAIL_MESSAGE, this.buildMsg(appResponse.getMsg()));
        }

        // 3 save cache
        redisService.set((String) toolContext.getContext().get(CommonConstants.KEY_CACHE_ID), JSON.toJSONString(toolCache), CommonConstants.SECONDS_ONE_HOUR);

        return message;
    }

    /**
     * 构建消息
     *
     * @param msg 消息
     * @return 构建后的消息
     */
    private String buildMsg(String msg) {
        log.info("[VehicleService][buildMsg][entrance] msg: {}", msg);
        if (StringUtils.isEmpty(msg)) {
            return MAINTENANCE_ERROR_MESSAGE;
        }

        try {
            List<String> msgList = JSON.parseArray(msg, String.class);

            StringBuilder result = new StringBuilder();
            for (String s : msgList) {
                result.append("- ").append(s).append("\n");
            }

            return result.toString();
        } catch (Exception e) {
            log.warn("[VehicleService][buildMsg][error] ", e);
            return msg;
        }
    }

}
