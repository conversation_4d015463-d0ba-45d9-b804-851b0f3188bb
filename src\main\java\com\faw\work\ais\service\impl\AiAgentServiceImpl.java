package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.aic.common.util.RedisService;
import com.faw.work.ais.aic.service.FaqKnowledgeService;
import com.faw.work.ais.aic.service.RagDocumentService;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.PromptConstants;
import com.faw.work.ais.common.dto.chat.AiAgentRequest;
import com.faw.work.ais.common.dto.chat.AiChatResponse;
import com.faw.work.ais.common.dto.chat.ToolCacheEntity;
import com.faw.work.ais.common.enums.chat.AnswerSourceEnum;
import com.faw.work.ais.common.enums.chat.ChatSceneEnum;
import com.faw.work.ais.common.enums.chat.ChatToolEnum;
import com.faw.work.ais.common.enums.chat.GenderEnum;
import com.faw.work.ais.common.util.DateUtils;
import com.faw.work.ais.config.chat.ChatRedisMemory;
import com.faw.work.ais.feign.chat.AliYunFeignClient;
import com.faw.work.ais.feign.chat.AppFeignClient;
import com.faw.work.ais.service.AiAgentService;
import com.faw.work.ais.service.tool.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbacks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Ai智能体 实现类
 *
 * <AUTHOR>
 * @since 2025-06-23 10:13
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AiAgentServiceImpl implements AiAgentService {

    private final ChatClient chatClient;

    private final RedisService redisService;

    private final ChatRedisMemory chatMemory;

    private final ChatClient universalClient;

    private final RagDocumentService ragDocumentService;

    private final FaqKnowledgeService faqKnowledgeService;

    private final AliYunFeignClient aliYunFeignClient;

    private final AppFeignClient appFeignClient;


    private static final Map<String, String> GENDER_MAP = Map.of(GenderEnum.FEMALE.getDesc(), GenderEnum.MALE.getDesc());


    @Override
    public String getAgentResponse(AiAgentRequest request) {
        log.info("[AiAgentService][getAgentResponse][entrance] request: {}", JSON.toJSONString(request));
        ChatSceneEnum intention = ChatSceneEnum.getByCode(request.getSceneCode());

        return switch (intention) {
            case ANALYZE_PHOTO -> new PhotoAnalysisService(aliYunFeignClient)
                    .analyzePhoto(request.getImageUrls(), request.getQuestion());
            case IDENTIFY_INTENT -> universalClient
                    .prompt(String.format(PromptConstants.IDENTIFY_INTENT_PROMPT, request.getQuestion()))
                    .call().content();
            case RECOMMEND -> universalClient
                    .prompt(String.format(PromptConstants.RECOMMEND_PROMPT, request.getQuestion()))
                    .call().content();
            default -> "未知智能体";
        };
    }

    @Override
    public Flux<AiChatResponse> getAgentFluxResponse(AiAgentRequest request) {
        log.info("[AiAgentService][getAgentFluxResponse][entrance] request: {}", JSON.toJSONString(request));
        // 1 init params
        String currentTime = DateUtils.formatDate(new Date(), DateUtils.FORMAT_DATE_TIME);

        // 2 build params
        String initPrompt;
        ToolCallback[] toolArray;
        Map<String, Object> toolContext = new HashMap<>(Map.of(CommonConstants.KEY_CACHE_ID, request.getCacheId()));

        ChatSceneEnum intention = ChatSceneEnum.getByCode(request.getSceneCode());
        toolArray = switch (intention) {
            case SMALL_TALK -> {
                String gender = GenderEnum.FEMALE.getDesc();
                if (StringUtils.isNotEmpty(request.getUserInfo().getGender())) {
                    gender = GENDER_MAP.getOrDefault(request.getUserInfo().getGender(), GenderEnum.FEMALE.getDesc());
                }

                initPrompt = String.format(PromptConstants.SMALL_TALK_PROMPT, gender);
                yield ToolCallbacks.from(new CommonService());
            }
            case KNOWLEDGE -> {
                initPrompt = PromptConstants.KNOWLEDGE_PROMPT;
                yield ToolCallbacks.from(new KnowledgeService(ragDocumentService, faqKnowledgeService, redisService));
            }
            case STAFF_SERVICE -> {
                initPrompt = PromptConstants.STAFF_SERVICE_PROMPT;
                yield ToolCallbacks.from(new FunctionService(redisService));
            }
            case PICK_UP_CAR -> {
                initPrompt = PromptConstants.PICK_UP_CAR_PROMPT;
                yield ToolCallbacks.from(new VehicleService(redisService, appFeignClient));
            }
            case MAINTENANCE -> {
                initPrompt = PromptConstants.MAINTENANCE_PROMPT;
                toolContext.put(CommonConstants.KEY_USER_INFO, request.getUserInfo());
                yield ToolCallbacks.from(new VehicleService(redisService, appFeignClient));
            }
            default -> {
                initPrompt = String.format(PromptConstants.CHAT_MODEL_INIT_PROMPT, currentTime);
                toolContext.put(CommonConstants.KEY_USER_INFO, request.getUserInfo());
                yield ToolCallbacks.from(
                        new CommonService(), new FunctionService(redisService), new VehicleService(redisService, appFeignClient),
                        new KnowledgeService(ragDocumentService, faqKnowledgeService, redisService));
            }
        };
        log.info("[AiAgentService][getAgentFluxResponse] initPrompt: {}", initPrompt);

        // 3 execute agent
        AiChatResponse response = AiChatResponse.builder()
                .answerSource(AnswerSourceEnum.BIG_MODEL.getCode()).sceneCode(request.getSceneCode()).build();

        return chatClient.prompt(initPrompt)
                .user(request.getQuestion())
                .advisors(new MessageChatMemoryAdvisor(chatMemory, request.getChatId(), CommonConstants.HUNDRED))
                .toolContext(toolContext)
                .tools(toolArray)
                .stream().chatResponse().map(
                        chatResponse -> this.afterChat(chatResponse, response, request.getCacheId()));
    }

    /**
     * 聊天后处理
     *
     * @param chatResponse 大模型响应
     * @param response     响应信息暂存对象
     * @return 接口响应
     */
    private AiChatResponse afterChat(ChatResponse chatResponse, AiChatResponse response, String cacheId) {
        log.info("[AiAgentService][afterChat][entrance] chatResponse: {}, response: {}", JSON.toJSONString(chatResponse), JSON.toJSONString(response));

        // 1 analysis toolCache
        String toolCacheStr = (String) redisService.get(cacheId);
        log.info("[AiAgentService][afterChat] toolCacheStr: {}", toolCacheStr);
        if (StringUtils.isNotEmpty(toolCacheStr)) {
            ToolCacheEntity toolCache = JSON.parseObject(toolCacheStr, ToolCacheEntity.class);

            if (ChatToolEnum.GET_VEHICLE_MANUAL.getName().equals(toolCache.getToolName())) {
                if (Boolean.TRUE.equals(toolCache.getToolStatus())) {
                    response.setAnswerSource(AnswerSourceEnum.KNOWLEDGE_BASE.getCode());
                } else {
                    response.setSceneCode(ChatSceneEnum.STAFF_SERVICE.getCode());
                }
            }
            else if (ChatToolEnum.MAINTENANCE.getName().equals(toolCache.getToolName())
                    && Boolean.TRUE.equals(toolCache.getToolStatus())) {
                response.setParams(toolCache.getToolValue());
            }
            else if (ChatToolEnum.STAFF_SERVICE.getName().equals(toolCache.getToolName())
                    && Boolean.FALSE.equals(toolCache.getToolStatus())) {
                response.setSceneCode(ChatSceneEnum.STAFF_SERVICE.getCode());
            }

            // delete cache
            redisService.delete(cacheId);
        }

        // 2 build response
        return AiChatResponse.builder()
                .content(chatResponse.getResult().getOutput().getText())
                .answerSource(response.getAnswerSource())
                .sceneCode(response.getSceneCode())
                .finishReason(chatResponse.getResult().getMetadata().getFinishReason())
                .params(response.getParams())
                .build();
    }

}
