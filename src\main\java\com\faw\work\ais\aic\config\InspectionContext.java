package com.faw.work.ais.aic.config;


import com.faw.work.ais.aic.model.domain.InspectionItemKeywordsPO;
import com.faw.work.ais.aic.model.domain.InspectionItemNegativePO;
import com.faw.work.ais.aic.model.domain.InspectionItemSimilarPO;
import com.faw.work.ais.aic.model.domain.InspectionItemVoicePO;
import com.faw.work.ais.aic.model.dto.EmbeddingPropertyDTO;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/**
 * 质检上下文，用于在策略间传递数据，避免重复计算和查询
 * <AUTHOR>
 */
@Getter
public class InspectionContext {

    private final String customerServiceSpeech;
    private EmbeddingPropertyDTO speechEmbedding;
    private final Map<Integer, List<InspectionItemKeywordsPO>> keywordsMap;
    private final Map<Integer, List<InspectionItemSimilarPO>> similarMap;
    private final Map<Integer, List<InspectionItemNegativePO>> negativeMap;
    private final Map<Integer, List<InspectionItemVoicePO>> voiceMap;

    public InspectionContext(String customerServiceSpeech, EmbeddingPropertyDTO speechEmbedding, Map<Integer, List<InspectionItemKeywordsPO>> keywordsMap, Map<Integer, List<InspectionItemSimilarPO>> similarMap, Map<Integer, List<InspectionItemNegativePO>> negativeMap, Map<Integer, List<InspectionItemVoicePO>> voiceMap) {
        this.customerServiceSpeech = customerServiceSpeech;
        this.speechEmbedding = speechEmbedding;
        this.keywordsMap = keywordsMap;
        this.similarMap = similarMap;
        this.negativeMap = negativeMap;
        this.voiceMap = voiceMap;
    }

    public void setSpeechEmbedding(EmbeddingPropertyDTO speechEmbedding) {
        this.speechEmbedding = speechEmbedding;
    }
}

