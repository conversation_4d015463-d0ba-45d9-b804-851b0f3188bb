package com.faw.work.ais.model;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
/**
* ai技术模型配置表
* Created  by Mr.hp
* DateTime on 2025-05-08 11:02:28
* <AUTHOR> Mr.hp
* @ApiModel(value = "AiTechnologyModel", description = "ai技术模型配置表")
*/
@Data
@NoArgsConstructor
@Schema(description = "人工智能ai技术模型配置表", name = "人工智能ai技术模型配置表")
public class AiTechnologyModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键id
    * @ApiModelProperty(value = "主键id")
    */
    @Schema(description = "主键id")
    private Long id;

    /**
    * 模型编码
    * @ApiModelProperty(value = "模型编码")
    */
    @Schema(description = "模型编码")
    private String technologyModelCode;

    /**
    * 模型名称
    */
    @Schema(description = "模型名称")
    private String technologyModelName;

    /**
    * 创建时间
    */
    @Schema(description = "创建时间")
    private String createTime;

    /**
    * 更新时间
    * @ApiModelProperty(value = "更新时间")
    */
    @Schema(description = "更新时间")
    private String updateTime;


}
