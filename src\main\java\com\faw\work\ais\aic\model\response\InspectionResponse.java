package com.faw.work.ais.aic.model.response;


import com.faw.work.ais.aic.model.dto.RuleResultDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 质检执行响应
 * <AUTHOR>
 */
@Data
@Schema(description = "质检执行响应")
public class InspectionResponse {

    @Schema(description = "最终总分", example = "95")
    private Integer totalScore;

    @Schema(description = "质检规则详情列表")
    private List<RuleResultDTO> rules;
}

