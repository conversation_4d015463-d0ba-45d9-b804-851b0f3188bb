package com.faw.work.ais.service.tool;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 通用服务类，提供基础信息
 *
 * <AUTHOR>
 * @since 2025-05-29 17:02
 */
@Slf4j
public class CommonService {

    @Tool(name = "getCurrentTime", description = "获取当前时间方法")
    public String getCurrentTime(ToolContext toolContext) {
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        log.info("[CommonService][getCurrentTime] currentTime: {}", currentTime);
        return String.format("当前时间为 %s。", currentTime);
    }

}
