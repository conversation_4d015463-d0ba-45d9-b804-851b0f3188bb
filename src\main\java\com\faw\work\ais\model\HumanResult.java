package com.faw.work.ais.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * human_result_new
 * <AUTHOR>
@Data
@ApiModel("人工审核结果记录表")
@TableName(value ="human_result_new")
public class HumanResult implements Serializable {

    @ApiModelProperty("主键")
    private Long id;

    /**
     * 人工审核结果-单次; 0-驳回；1-通过；单次
     */
    @ApiModelProperty("人工审核结果-单次; 0-驳回；1-通过；单次")
    private String humanCheckResultSingle;

    /**
     * 人工驳回原因；
     */
    @ApiModelProperty("人工驳回原因；")
    private String humanRefuseReason;

    /**
     * 审核单据的批次id
     */
    @ApiModelProperty("审核单据的批次id")
    private String batchId;

    /**
     * 调用一次AI审核的唯一id
     */
    @ApiModelProperty("调用一次AI审核的唯一id")
    private String traceId;

    /**
     * 业务主键，不唯一
     */
    @ApiModelProperty("业务主键，不唯一")
    private String bizId;

    /**
     * 业务主键，不唯一
     */
    @ApiModelProperty("业务类型，不唯一")
    private String bizType;

    /**
     * 规则名称
     */
    @ApiModelProperty("规则代码")
    private String taskType;

    /**
     * 人工审核时间
     */
    @ApiModelProperty("人工审核时间")
    private Date humanCheckTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 系统id
     */
    @ApiModelProperty("系统id")
    private String systemId;

    /**
     * 人工审核结果-单据维度; 0-驳回；1-通过；按单据维度，多次审核一次失败就为驳回，全部通过算通过；
     */
    @ApiModelProperty("人工审核结果-单据维度; 0-驳回；1-通过；按单据维度，多次审核一次失败就为驳回，全部通过算通过；")
    private String humanCheckResultFinal;

    @Schema(description = "AI审核结果-规则维度；0-驳回；1-通过；")
    private String aiResultSingle;

    @Schema(description = "AI审核结果-单据维度；0-驳回；1-通过")
    private String aiResultFinal;

    @Schema(description = "AI审核时间")
    private Date aiResultTime;

    private static final long serialVersionUID = 1L;
}