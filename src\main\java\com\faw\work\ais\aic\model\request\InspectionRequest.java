package com.faw.work.ais.aic.model.request;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 质检执行请求
 * <AUTHOR>
 */
@Data
@Schema(description = "质检执行请求")
public class InspectionRequest {

    @NotNull(message = "质检方案ID不能为空")
    @Schema(description = "质检方案ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "581")
    private Long schemeId;

    @NotBlank(message = "客服发言内容不能为空")
    @Schema(description = "客服的发言内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "您好，欢迎试驾EH7，现在到店试驾还可以领取试驾礼品。")
    private String customerServiceSpeech;
}

