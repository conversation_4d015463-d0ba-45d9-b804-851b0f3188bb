package com.faw.work.ais.mapper.ais;

import com.faw.work.ais.example.PrdRuleTestExample;
import com.faw.work.ais.model.PrdRuleTest;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PrdRuleTestMapper {
    /**
     * 获取Prd测试结果的数据
     * @return
     */
    List<PrdRuleTest> getPrdTestTestData(@Param("param") PrdRuleTest prdRuleTest);
    long countByExample(PrdRuleTestExample example);

    int deleteByExample(PrdRuleTestExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(PrdRuleTest record);

    int insertSelective(PrdRuleTest record);

    List<PrdRuleTest> selectByExampleWithBLOBs(PrdRuleTestExample example);

    List<PrdRuleTest> selectByExample(PrdRuleTestExample example);

    PrdRuleTest selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") PrdRuleTest record, @Param("example") PrdRuleTestExample example);

    int updateByExampleWithBLOBs(@Param("record") PrdRuleTest record, @Param("example") PrdRuleTestExample example);

    int updateByExample(@Param("record") PrdRuleTest record, @Param("example") PrdRuleTestExample example);

    int updateByPrimaryKeySelective(PrdRuleTest record);

    int updateByPrimaryKeyWithBLOBs(PrdRuleTest record);

    int updateByPrimaryKey(PrdRuleTest record);
}