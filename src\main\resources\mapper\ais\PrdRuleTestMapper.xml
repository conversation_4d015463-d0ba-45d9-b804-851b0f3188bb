<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.PrdRuleTestMapper">
  <resultMap id="BaseResultMap" type="com.faw.work.ais.model.PrdRuleTest">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="system_id" jdbcType="VARCHAR" property="systemId" />
    <result column="traceid" jdbcType="VARCHAR" property="traceid" />
    <result column="human_result" jdbcType="VARCHAR" property="humanResult" />
    <result column="human_score" jdbcType="VARCHAR" property="humanScore" />
    <result column="ai_result" jdbcType="VARCHAR" property="aiResult" />
    <result column="raw_result" jdbcType="VARCHAR" property="rawResult" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.faw.work.ais.model.PrdRuleTest">
    <result column="given_info_json" jdbcType="LONGVARCHAR" property="givenInfoJson" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, url, create_time, update_time, task_type, system_id, traceid, human_result, human_score, 
    ai_result, raw_result
  </sql>
  <sql id="Blob_Column_List">
    given_info_json
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.faw.work.ais.example.PrdRuleTestExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from prd_rule_test
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.faw.work.ais.example.PrdRuleTestExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from prd_rule_test
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from prd_rule_test
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from prd_rule_test
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.faw.work.ais.example.PrdRuleTestExample">
    delete from prd_rule_test
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.faw.work.ais.model.PrdRuleTest" useGeneratedKeys="true">
    insert into prd_rule_test (url, create_time, update_time, 
      task_type, system_id, traceid, 
      human_result, human_score, ai_result, 
      raw_result, given_info_json, ai_score)
    values (#{url,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{taskType,jdbcType=VARCHAR}, #{systemId,jdbcType=VARCHAR}, #{traceid,jdbcType=VARCHAR}, 
      #{humanResult,jdbcType=VARCHAR}, #{humanScore,jdbcType=VARCHAR}, #{aiResult,jdbcType=VARCHAR}, 
      #{rawResult,jdbcType=VARCHAR}, #{givenInfoJson,jdbcType=LONGVARCHAR}, #{aiScore,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.faw.work.ais.model.PrdRuleTest" useGeneratedKeys="true">
    insert into prd_rule_test
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="url != null">
        url,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="systemId != null">
        system_id,
      </if>
      <if test="traceid != null">
        traceid,
      </if>
      <if test="humanResult != null">
        human_result,
      </if>
      <if test="humanScore != null">
        human_score,
      </if>
      <if test="aiResult != null">
        ai_result,
      </if>
      <if test="rawResult != null">
        raw_result,
      </if>
      <if test="givenInfoJson != null">
        given_info_json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null">
        #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="traceid != null">
        #{traceid,jdbcType=VARCHAR},
      </if>
      <if test="humanResult != null">
        #{humanResult,jdbcType=VARCHAR},
      </if>
      <if test="humanScore != null">
        #{humanScore,jdbcType=VARCHAR},
      </if>
      <if test="aiResult != null">
        #{aiResult,jdbcType=VARCHAR},
      </if>
      <if test="rawResult != null">
        #{rawResult,jdbcType=VARCHAR},
      </if>
      <if test="givenInfoJson != null">
        #{givenInfoJson,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.faw.work.ais.example.PrdRuleTestExample" resultType="java.lang.Long">
    select count(*) from prd_rule_test
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update prd_rule_test
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.url != null">
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=VARCHAR},
      </if>
      <if test="record.systemId != null">
        system_id = #{record.systemId,jdbcType=VARCHAR},
      </if>
      <if test="record.traceid != null">
        traceid = #{record.traceid,jdbcType=VARCHAR},
      </if>
      <if test="record.humanResult != null">
        human_result = #{record.humanResult,jdbcType=VARCHAR},
      </if>
      <if test="record.humanScore != null">
        human_score = #{record.humanScore,jdbcType=VARCHAR},
      </if>
      <if test="record.aiResult != null">
        ai_result = #{record.aiResult,jdbcType=VARCHAR},
      </if>
      <if test="record.rawResult != null">
        raw_result = #{record.rawResult,jdbcType=VARCHAR},
      </if>
      <if test="record.givenInfoJson != null">
        given_info_json = #{record.givenInfoJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update prd_rule_test
    set id = #{record.id,jdbcType=INTEGER},
      url = #{record.url,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      task_type = #{record.taskType,jdbcType=VARCHAR},
      system_id = #{record.systemId,jdbcType=VARCHAR},
      traceid = #{record.traceid,jdbcType=VARCHAR},
      human_result = #{record.humanResult,jdbcType=VARCHAR},
      human_score = #{record.humanScore,jdbcType=VARCHAR},
      ai_result = #{record.aiResult,jdbcType=VARCHAR},
      raw_result = #{record.rawResult,jdbcType=VARCHAR},
      given_info_json = #{record.givenInfoJson,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update prd_rule_test
    set id = #{record.id,jdbcType=INTEGER},
      url = #{record.url,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      task_type = #{record.taskType,jdbcType=VARCHAR},
      system_id = #{record.systemId,jdbcType=VARCHAR},
      traceid = #{record.traceid,jdbcType=VARCHAR},
      human_result = #{record.humanResult,jdbcType=VARCHAR},
      human_score = #{record.humanScore,jdbcType=VARCHAR},
      ai_result = #{record.aiResult,jdbcType=VARCHAR},
      raw_result = #{record.rawResult,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.faw.work.ais.model.PrdRuleTest">
    update prd_rule_test
    <set>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="systemId != null">
        system_id = #{systemId,jdbcType=VARCHAR},
      </if>
      <if test="traceid != null">
        traceid = #{traceid,jdbcType=VARCHAR},
      </if>
      <if test="humanResult != null">
        human_result = #{humanResult,jdbcType=VARCHAR},
      </if>
      <if test="humanScore != null">
        human_score = #{humanScore,jdbcType=VARCHAR},
      </if>
      <if test="aiResult != null">
        ai_result = #{aiResult,jdbcType=VARCHAR},
      </if>
      <if test="rawResult != null">
        raw_result = #{rawResult,jdbcType=VARCHAR},
      </if>
      <if test="givenInfoJson != null">
        given_info_json = #{givenInfoJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.faw.work.ais.model.PrdRuleTest">
    update prd_rule_test
    set url = #{url,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      task_type = #{taskType,jdbcType=VARCHAR},
      system_id = #{systemId,jdbcType=VARCHAR},
      traceid = #{traceid,jdbcType=VARCHAR},
      human_result = #{humanResult,jdbcType=VARCHAR},
      human_score = #{humanScore,jdbcType=VARCHAR},
      ai_result = #{aiResult,jdbcType=VARCHAR},
      raw_result = #{rawResult,jdbcType=VARCHAR},
      given_info_json = #{givenInfoJson,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.faw.work.ais.model.PrdRuleTest">
    update prd_rule_test
    set url = #{url,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      task_type = #{taskType,jdbcType=VARCHAR},
      system_id = #{systemId,jdbcType=VARCHAR},
      traceid = #{traceid,jdbcType=VARCHAR},
      human_result = #{humanResult,jdbcType=VARCHAR},
      human_score = #{humanScore,jdbcType=VARCHAR},
      ai_result = #{aiResult,jdbcType=VARCHAR},
      raw_result = #{rawResult,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getPrdTestTestData" resultType="com.faw.work.ais.model.PrdRuleTest">
    SELECT t.*
    FROM prd_rule_test_data t
    <where>
      <if test="param.id != null and param.id != ''">
        AND t.id = #{param.id}
      </if>
    </where>
  </select>

</mapper>