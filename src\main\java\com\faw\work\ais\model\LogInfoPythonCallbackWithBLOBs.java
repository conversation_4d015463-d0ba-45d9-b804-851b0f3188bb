package com.faw.work.ais.model;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * log_info_python_callback_new
 * <AUTHOR>
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value ="log_info_python_callback_new")
public class LogInfoPythonCallbackWithBLOBs extends LogInfoPythonCallback implements Serializable {
    /**
     * 调用python服务的入参
     */
    private String pythonParam;

    /**
     * python服务返回结果
     */
    private String pythonResultJson;

    /**
     * 回调入参
     */
    private String callbackParam;

    /**
     * 回调结果
     */
    private String callbackResult;

    private static final long serialVersionUID = 1L;
}