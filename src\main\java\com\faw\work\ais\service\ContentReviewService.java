package com.faw.work.ais.service;

import com.alibaba.fastjson.JSONObject;
import com.faw.work.ais.entity.domain.ContentRulePO;
import com.faw.work.ais.entity.dto.ContentReviewResult;
import com.faw.work.ais.entity.dto.ContentSearchRes;
import com.faw.work.ais.entity.request.*;
import jakarta.validation.Valid;

import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface ContentReviewService {

    /**
     * 相似内容
     * @param content
     * @param topK
     * @return
     */
    ContentSearchRes similarContent(String content, int topK);

    /**
     * 内容存储
     * @param content
     * @param totalScore
     * @param embedding
     */
    void storeEmbedding(String content, Integer totalScore, float[] embedding);

    /**
     * 评论评分(带查重)
     * @param request
     * @param embedding
     * @return
     */
    List<ContentReviewResult> commentScore(CommentRequest request, float[] embedding);

    /**
     * 评论评分
     * @param request
     * @param times
     * @return
     */
    JSONObject commentReview(CommentRequest request, Integer times);

    /**
     * 评论评分v1
     * @param request
     * @param times
     * @return
     */
    JSONObject commentReview_v1(CommentRequest request, Integer times);


    /**
     * 动态评分
     * @param request
     * @param times
     * @return
     */
    JSONObject postScore(PostRequest request, Integer times, Boolean flag);

    /**
     * 动态评分（带去重）
     * @param request
     * @param times
     * @return
     */
    JSONObject postReview(PostRequest request, Integer times);

    /**
     * 动态评分v1
     * @param request
     * @param times
     * @return
     */
    JSONObject postReview_v1(PostRequest request, Integer times);
    /**
     * 评论总结
     * @param requests
     * @return
     */
    void commentSummary(@Valid List<CommentSummaryRequest> requests);

    /**
     * 动态总结
     * @param requests
     * @return
     */
    void postSummary(@Valid List<PostSummaryRequest> requests);


    JSONObject postReviewTruth(PostRequest request, int topK);

    String editRule(ContentRulePO request);

    String deleteRule(Long id);
}
