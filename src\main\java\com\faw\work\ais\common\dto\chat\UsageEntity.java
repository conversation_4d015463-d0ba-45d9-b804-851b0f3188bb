package com.faw.work.ais.common.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * 类描述
 *
 * <AUTHOR>
 * @since 2025-04-22 16:38
 */
@Data
public class UsageEntity {

    @JsonProperty("prompt_tokens")
    private Integer promptTokens;

    @JsonProperty("completion_tokens")
    private Integer completionTokens;

    @JsonProperty("total_tokens")
    private Integer totalTokens;

}
