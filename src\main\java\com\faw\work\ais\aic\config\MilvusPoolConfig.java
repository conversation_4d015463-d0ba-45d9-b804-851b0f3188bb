package com.faw.work.ais.aic.config;

import io.milvus.pool.MilvusClientV2Pool;
import io.milvus.pool.PoolConfig;
import io.milvus.v2.client.ConnectConfig;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.service.collection.request.HasCollectionReq;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
@Data
public class MilvusPoolConfig {
    final int MAX_RETRIES = 3;

    /**
     * 客户端租户标识
     */
    public static final String FAQ_TENANT_CLIENT_KEY = "faq_tenant_client_key";

    public static final String RAG_TENANT_CLIENT_KEY = "rag_tenant_client_key";


    public static final String DEFAULT_TENANT_CLIENT_KEY = "default_tenant_client_key";


    public static final String ID_FIELD = "id";

    public static final String VECTOR_FIELD = "embedding";

    public static final String BIZ_INFO_FIELD = "biz_info";
    public static final String LABEL = "label";
    public static final String AGENT_ID = "agent_id";

    public static final String DOCUMENT_ID_FIELD = "document_id";

    public static final String FAQ_COLLECTION_NAME = "faq_knowledge";
    public static final String FAQ_COLLECTION_NAME_PROD = "faq_knowledge_prod";

    public static final String INSPECTION_SIMILARITY_RULES = "inspection_similarity_rules";

    public static final String COMMENT_COLLECTION_NAME = "high_quality_comments";

    public static final String POST_COLLECTION_NAME = "high_quality_posts";

    public static final String CONTENT = "content";

    public static final String VEHICLE_INFO = "vehicle_info";

    @Autowired
    private RedissonClient redissonClient;

    @Value("${milvus.host:localhost}")
    private String host;

    @Value("${milvus.port:19530}")
    private int port;

    @Value("${milvus.username:}")
    private String username;

    @Value("${milvus.password:}")
    private String password;

    @Value("${milvus.dbName:default}")
    private String dbName;

    @Value("${milvus.collection.name:knowledge_base}")
    private String collectionName;


    @Value("${milvus.pool.maxIdlePerKey:5}")
    private int maxIdlePerKey;

    @Value("${milvus.pool.maxTotalPerKey:30}")
    private int maxTotalPerKey;

    @Value("${milvus.pool.maxTotal:200}")
    private int maxTotal;

    @Value("${milvus.pool.maxBlockWaitSeconds:5}")
    private long maxBlockWaitSeconds;

    @Value("${milvus.pool.minEvictableIdleSeconds:30}")
    private long minEvictableIdleSeconds;

    @Autowired
    private ApplicationContext applicationContext;

    private MilvusClientV2Pool pool;

    @Bean
    public MilvusClientV2Pool milvusClientPool() {
        log.info("Initializing Milvus client pool with host: {}, port: {}, dbName: {}", host, port, dbName);

        ConnectConfig connectConfig = ConnectConfig.builder()
                .uri(host + ":" + port)
                .username(username)
                .password(password)
                .dbName(dbName)
                .connectTimeoutMs(5000L)
                .build();

        PoolConfig poolConfig = PoolConfig.builder()
                .maxIdlePerKey(maxIdlePerKey)
                .maxTotalPerKey(maxTotalPerKey)
                .maxTotal(maxTotal)
                .maxBlockWaitDuration(Duration.ofSeconds(maxBlockWaitSeconds))
                .minEvictableIdleDuration(Duration.ofSeconds(minEvictableIdleSeconds))
                .testOnBorrow(true)
                .testOnReturn(false)
                .build();

        try {
            pool = new MilvusClientV2Pool(poolConfig, connectConfig);
            if (testConnection(pool)) {
                log.info("Milvus client pool initialized successfully with {} max connections", maxTotal);
            } else {
                log.warn("Milvus client pool initialized but connection test failed");
            }
        } catch (Exception e) {
            log.error("Failed to initialize Milvus client pool: {}", e.getMessage(), e);
        }
        return pool;
    }


    public MilvusClientV2 getClient(String key) {
        // 快速路径 - 最常见的情况
        if (pool != null) {
            if (log.isDebugEnabled()) {
                logPoolStatus();
            }
            return pool.getClient(key);
        }

        // 慢速路径 - 需要初始化
        return getClientWithInitialization(key);
    }

    private MilvusClientV2 getClientWithInitialization(String key) {
        // 本地重试计数器，防止无限循环
        int retryCount = 0;

        while (pool == null && retryCount < MAX_RETRIES) {
            retryCount++;
            log.info("Milvus客户端池未初始化，尝试 {} / {}", retryCount, MAX_RETRIES);

            // Redis锁，为高QPS设置更短的超时
            String lockName = "milvus_pool_init_lock";
            RLock lock = redissonClient.getLock(lockName);
            boolean isLock = false;

            try {
                // 更短的等待时间（100ms而不是500ms）
                isLock = lock.tryLock(100, 10000, TimeUnit.MILLISECONDS);

                if (isLock) {
                    if (pool == null) {
                        try {
                            pool = reinitializePool();
                            log.info("Milvus客户端池初始化成功");
                            break; // 成功后退出重试循环
                        } catch (Exception e) {
                            log.error("初始化Milvus客户端池失败", e);
                        }
                    } else {
                        log.debug("池已被另一个线程初始化");
                        break; // 退出重试循环
                    }
                } else if (retryCount < MAX_RETRIES) {
                    // 重试前短暂等待以减少竞争
                    Thread.sleep(50L * retryCount);
                    log.debug("等待另一个实例完成初始化");
                } else {
                    log.warn("等待池初始化达到最大重试次数");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("等待初始化锁时被中断", e);
            } finally {
                if (isLock && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }

        // 最终检查 - 如果重试后池仍为null，返回null并警告
        if (pool == null) {
            log.warn("在 {} 次尝试后未能初始化或访问Milvus客户端池", MAX_RETRIES);
            return null;
        }

        if (log.isDebugEnabled()) {
            logPoolStatus();
        }

        return pool.getClient(key);
    }
    /**
     * 释放Milvus客户端回池
     *
     * @param key    客户端标识
     * @param client Milvus客户端实例
     */
    public void releaseClient(String key, MilvusClientV2 client) {
        if (pool != null && client != null) {
            pool.returnClient(key, client);
        }
    }

    /**
     * 获取连接池状态信息
     */
    public void logPoolStatus() {
        if (pool != null) {
            try {
                log.debug("连接池状态 - 活跃连接数: {}, 空闲连接数: {}",
                        pool.getTotalActiveClientNumber(),
                        pool.getTotalIdleClientNumber());
            } catch (Exception e) {
                log.warn("获取连接池状态失败: {}", e.getMessage());
                // 不要在这里尝试重新初始化 - 在高QPS下很危险
            }
        } else {
            log.warn("连接池对象为null，无法获取状态");
        }
    }

    /**
     * 安全地重新初始化连接池
     *
     * @return 新创建的连接池
     */
    private MilvusClientV2Pool reinitializePool() {
        log.info("安全地重新初始化Milvus连接池");

        // 先关闭旧连接池，避免资源泄漏
        if (pool != null) {
            try {
                pool.close();
                log.info("成功关闭旧连接池");
            } catch (Exception e) {
                log.warn("关闭旧连接池时发生异常", e);
            }
        }

        // 通过Spring上下文获取自身实例，确保调用的是Spring管理的Bean方法
        MilvusPoolConfig self = applicationContext.getBean(MilvusPoolConfig.class);
        // 调用Spring管理的Bean方法创建新连接池
        return self.milvusClientPool();
    }


    private boolean testConnection(MilvusClientV2Pool testPool) {
        if (testPool == null) {
            return false;
        }

        MilvusClientV2 client = null;
        try {
            client = testPool.getClient("health_check");
            if (client == null) {
                log.error("Milvus客户端为null");
                return false;
            }
            boolean healthy = client.hasCollection(HasCollectionReq.builder()
                    .collectionName("not_exist_collection").build());
            log.info("Milvus连接健康检查通过hasCollection: {}", healthy);
            return true;
        } catch (Exception e) {
            log.error("Milvus健康检查失败: {}", e.getMessage());
            return false;
        } finally {
            if (client != null) {
                try {
                    testPool.returnClient("health_check", client);
                } catch (Exception e) {
                    log.warn("健康检查期间归还客户端到池失败", e);
                }
            }
        }
    }
}