package com.faw.work.ais.aic.service;


import com.faw.work.ais.aic.model.request.InspectionRequest;
import com.faw.work.ais.aic.model.response.InspectionResponse;

/**
 * 质检服务接口
 * <AUTHOR>
 */
public interface InspectionService {

    /**
     * 执行质检
     *
     * @param request 质检请求，包含方案ID和待检文本
     * @return 质检结果
     */
    InspectionResponse execute(InspectionRequest request);


    /**
     * 初始化相似度规则数据到Milvus。
     * <p>
     * 该方法会读取数据库中所有启用的相似度规则，
     * 将其文本内容通过Embedding服务转换为向量，
     * 然后将向量及关联元数据（如规则ID）写入或更新到Milvus集合中。
     * 这是一个管理操作，用于数据同步。
     * </p>
     *
     * @param inspectionRuleId 检查方案id
     */
    void initializeSimilarityData(Long inspectionRuleId);
}

