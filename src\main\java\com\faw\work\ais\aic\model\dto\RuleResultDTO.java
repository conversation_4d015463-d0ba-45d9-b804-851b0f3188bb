package com.faw.work.ais.aic.model.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 单条规则的质检结果
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "单条规则的质检结果")
public class RuleResultDTO {

    @Schema(description = "规则ID", example = "18700")
    private Integer ruleId;

    @Schema(description = "规则名称", example = "活动信息-EH7")
    private String ruleName;

    @Schema(description = "规则得分（命中时）", example = "15")
    private Integer score;

    @Schema(description = "是否命中规则", example = "true")
    private Boolean isMatched;

    @Schema(description = "命中的质检项列表")
    private List<MatchedItemDTO> matchedItems;
}

