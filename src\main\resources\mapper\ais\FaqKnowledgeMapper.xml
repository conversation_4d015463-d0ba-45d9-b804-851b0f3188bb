<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.faq.FaqKnowledgeMapper">


    <!-- 分页查询知识列表 -->
    <select id="selectPageWithKeyword" resultType="com.faw.work.ais.aic.model.domain.FaqKnowledgePO">
        SELECT
        t1.*,
        t2.name AS categoryName,
        t3.robot_name AS robotName
        FROM
        faq_knowledge t1
        INNER JOIN faq_category t2 ON t1.category_id = t2.id
        LEFT JOIN faq_robot_knowledge_joins t4 ON t1.id = t4.knowledge_id
        LEFT JOIN faq_robot t3 ON t4.robot_id = t3.id
        <where>
            <if test="categoryIds != null and categoryIds.size > 0">
                AND t1.category_id IN
                <foreach collection="categoryIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="type=='00'">
                    <if test="text != null and text != ''">
                        AND t1.question LIKE CONCAT('%', #{text}, '%')
                    </if>
                </when>
                <when test="type=='01'">
                    <if test="text != null and text != ''">
                        AND t1.answer LIKE CONCAT('%', #{text}, '%')
                    </if>
                </when>
            </choose>
        </where>
        ORDER BY t1.updated_at DESC
    </select>

    <!-- 使用 PageHelper 分页查询知识列表，通过 faq_robot_knowledge_joins 表进行关联 -->
    <select id="selectPageWithKeywordByPageHelper" resultType="com.faw.work.ais.aic.model.domain.FaqKnowledgePO">
        SELECT DISTINCT
        t1.*,
        t2.name AS categoryName
        FROM
        faq_knowledge t1
        INNER JOIN faq_category t2 ON t1.category_id = t2.id
        LEFT JOIN faq_robot_knowledge_joins t4 ON t1.id = t4.knowledge_id
        <where>
            <if test="categoryIds != null and categoryIds.size > 0">
                AND t1.category_id IN
                <foreach collection="categoryIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="type=='00'">
                    <if test="text != null and text != ''">
                        AND t1.question LIKE CONCAT('%', #{text}, '%')
                    </if>
                </when>
                <when test="type=='01'">
                    <if test="text != null and text != ''">
                        AND t1.answer LIKE CONCAT('%', #{text}, '%')
                    </if>
                </when>
            </choose>
            <if test="robotId != null and robotId != ''">
                AND t4.robot_id = #{robotId}
            </if>
        </where>
        ORDER BY t1.updated_at DESC
    </select>

    <!-- 增加命中次数 -->
    <update id="incrementHitCount">
        UPDATE faq_knowledge
        SET hit_count = IFNULL(hit_count, 0) + 1
        WHERE id = #{id}
    </update>

    <!-- 根据ID列表查询知识（包括相似问查询） -->
    <select id="selectByIdsWithSimilar" resultType="com.faw.work.ais.aic.model.domain.FaqKnowledgePO">
        SELECT fk.id, fk.question, fk.answer, fk.hit_count, fk.category_id,
        fk.created_by, fk.created_at, fk.updated_by, fk.updated_at,
        sq.similar_question, sq.knowledge_id, 'origin' as source, #{score} as score
        FROM faq_knowledge fk
        INNER JOIN faq_robot_knowledge_joins rj ON fk.id = rj.knowledge_id
        LEFT JOIN faq_similar_knowledge sq ON sq.knowledge_id = fk.id
        WHERE fk.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND rj.robot_id = #{robotId}

        UNION ALL

        SELECT fk.id, fk.question, fk.answer, fk.hit_count, fk.category_id,
        fk.created_by, fk.created_at, fk.updated_by, fk.updated_at,
        sq.similar_question, sq.knowledge_id, 'similar' as source, #{score} as score
        FROM faq_similar_knowledge sq
        INNER JOIN faq_knowledge fk ON sq.knowledge_id = fk.id
        INNER JOIN faq_robot_knowledge_joins rj ON fk.id = rj.knowledge_id
        WHERE sq.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND rj.robot_id = #{robotId}
    </select>

    <!-- 根据ID列表从关联表查询知识 -->
    <select id="selectFromFaqKnowledgeAndFaqSimilarQuestionByIds"
            resultType="com.faw.work.ais.aic.model.response.FaqKnowledgeResponse">
        SELECT
        sq.id,
        sq.question,
        sq.answer,
        sq.similar_question,
        'similar' AS source,
        rj.id as joinId,
        sq.category_id as categoryId,
        sq.knowledge_id as knowledgeId
        FROM
        <choose>
            <when test="env == 'prod'">
                faq_similar_knowledge_prod sq
                INNER JOIN
                faq_robot_knowledge_joins_prod rj ON sq.id = rj.knowledge_id
            </when>
            <when test="env == 'test'">
                faq_similar_knowledge sq
                INNER JOIN
                faq_robot_knowledge_joins rj ON sq.id = rj.knowledge_id
            </when>
        </choose>
        WHERE
        rj.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND rj.robot_id = #{robotId}
        AND rj.source = 'similar'

        UNION ALL

        SELECT
        fk.id,
        fk.question,
        fk.answer,
        NULL AS similar_question,
        'origin' AS source,
        rj.id as joinId,
        fk.category_id as categoryId,
        fk.id as knowledgeId
        FROM
        <choose>
            <when test="env == 'prod'">
                faq_knowledge_prod fk
                INNER JOIN
                faq_robot_knowledge_joins_prod rj ON fk.id = rj.knowledge_id
            </when>
            <when test="env == 'test'">
                faq_knowledge fk
                INNER JOIN
                faq_robot_knowledge_joins rj ON fk.id = rj.knowledge_id
            </when>
        </choose>
        WHERE
        rj.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND rj.robot_id = #{robotId}
        AND rj.source = 'original'
    </select>


    <!-- 根据ID列表从FAQ知识表查询知识 -->
    <select id="selectFromFaqKnowledgeByIds" resultType="com.faw.work.ais.aic.model.response.FaqKnowledgeResponse">
        SELECT fk.id, fk.question, fk.answer, fk.hit_count,
        null, 'origin' as source, #{score} as score
        FROM faq_knowledge fk
        INNER JOIN faq_robot_knowledge_joins rj ON fk.id = rj.knowledge_id
        INNER JOIN faq_robot fr ON rj.robot_id = fr.id

        WHERE fk.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND rj.robot_id = #{robotId}
    </select>

    <!-- 根据条件查询所有FAQ知识 -->
    <select id="selectAllByCondition" resultType="com.faw.work.ais.aic.model.domain.FaqKnowledgePO">
        SELECT
        *
        FROM faq_knowledge
        <where>
            <if test="categoryIdList != null and categoryIdList.size() > 0">
                AND category_id IN
                <foreach item="categoryId" collection="categoryIdList" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>
            <if test="question != null and question != ''">
                AND question LIKE CONCAT('%', #{question}, '%')
            </if>
            <if test="answer != null and answer != ''">
                AND answer LIKE CONCAT('%', #{answer}, '%')
            </if>
            <if test="createdBy != null and createdBy != ''">
                AND created_by = #{createdBy}
            </if>
        </where>
    </select>

    <!-- 根据知识ID查询绑定的机器人ID列表 -->
    <select id="selectRobotIdsByKnowledgeId" resultType="java.lang.String">
        SELECT DISTINCT robot_id
        FROM faq_robot_knowledge_joins
        WHERE knowledge_id = #{knowledgeId}
    </select>
    <select id="selectByCategoryIds" resultType="com.faw.work.ais.aic.model.domain.FaqKnowledgePO">
        SELECT
        *
        FROM faq_knowledge
        WHERE category_id IN
        <foreach item="categoryId" collection="categoryIds" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </select>
    <select id="selectProdKnowledgeById" resultType="com.faw.work.ais.aic.model.domain.FaqKnowledgePO">
        SELECT *
        FROM faq_knowledge_prod
        WHERE id = #{id}
    </select>
    <select id="selectProdPageWithKeyword" resultType="com.faw.work.ais.aic.model.domain.FaqKnowledgePO">
        SELECT
        t1.*,t2.name as categoryName
        FROM faq_knowledge_prod t1 INNER JOIN faq_category_prod t2
        ON category_id = t2.id
        <where>
            <if test="categoryIds != null and categoryIds.size > 0">
                and t1.category_id in
                <foreach collection="categoryIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="type=='00'">
                    <if test="text != null and text != ''">
                        AND t1.question LIKE CONCAT('%', #{text}, '%')
                    </if>
                </when>
                <when test="type=='01'">
                    <if test="text != null and text != ''">
                        AND t1.answer LIKE CONCAT('%', #{text}, '%')
                    </if>
                </when>
            </choose>
        </where>
        ORDER BY updated_at DESC
    </select>

    <!-- 使用 PageHelper 分页查询生产环境知识列表，通过 faq_robot_knowledge_joins_prod 表进行关联 -->
    <select id="selectProdPageWithKeywordByPageHelper" resultType="com.faw.work.ais.aic.model.domain.FaqKnowledgePO">
        SELECT DISTINCT
        t1.*,
        t2.name AS categoryName
        FROM
        faq_knowledge_prod t1
        INNER JOIN faq_category_prod t2 ON t1.category_id = t2.id
        LEFT JOIN faq_robot_knowledge_joins_prod t4 ON t1.id = t4.knowledge_id
        <where>
            <if test="categoryIds != null and categoryIds.size > 0">
                AND t1.category_id IN
                <foreach collection="categoryIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="type=='00'">
                    <if test="text != null and text != ''">
                        AND t1.question LIKE CONCAT('%', #{text}, '%')
                    </if>
                </when>
                <when test="type=='01'">
                    <if test="text != null and text != ''">
                        AND t1.answer LIKE CONCAT('%', #{text}, '%')
                    </if>
                </when>
            </choose>
            <if test="robotId != null and robotId != ''">
                AND t4.robot_id = #{robotId}
            </if>
        </where>
        ORDER BY t1.updated_at DESC
    </select>
    <select id="selectDistinctCountByCategoryId" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT category_id)
        FROM faq_knowledge
        WHERE category_id IN
        <foreach item="categoryId" collection="categoryIds" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </select>
    <select id="selectKnowledgeIdsByCategoryIds" resultType="java.lang.String">
        select id
        from faq_knowledge
        where category_id in
        <foreach item="categoryId" collection="categoryIdList" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </select>
    <select id="selectIdAndQuestionByIds" resultType="com.faw.work.ais.aic.model.domain.FaqKnowledgePO">
        SELECT
        id,
        question
        FROM
        faq_knowledge
        WHERE
        id IN
        <foreach collection="originalKnowledgeIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="countEffectiveKnowledgeForRobot" resultType="long">
        SELECT COUNT(id)
        <choose>
            <when test="env == 'prod'">
                FROM faq_knowledge_prod
            </when>
            <otherwise>
                FROM faq_knowledge
            </otherwise>
        </choose>
        WHERE id IN
        <foreach item="item" index="index" collection="knowledgeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        -- AND publish_status = '01' -- 已发布
        AND (
        effectiveType = '00' -- 永久有效
        OR (
        effectiveType = '01'
        AND effectiveStartTime &lt;= #{date}
        AND effectiveEndTime &gt;= #{date}  <!-- 修复：结束时间判断 -->
        )
        )
    </select>
    <select id="selectBatchIdsByEnv" resultType="com.faw.work.ais.aic.model.domain.FaqKnowledgePO">
        SELECT *
        <choose>
            <when test="env == 'prod'">
                FROM faq_knowledge_prod
            </when>
            <otherwise>
                FROM faq_knowledge
            </otherwise>
        </choose>
        WHERE id IN
        <foreach item="item" collection="coldKnowledgeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="dogKnowledgeCount" resultType="java.lang.Integer">
        SELECT count(t1.id)
        FROM faq_knowledge t1
                 INNER JOIN faq_robot_knowledge_joins t2
                            ON t1.id = t2.knowledge_id
        WHERE t2.robot_id = #{robotId}
          AND t1.knowledge_version = #{knowledgeVersion}
    </select>
    <select id="findBoundCategoryIds" parameterType="list" resultType="string">
        SELECT DISTINCT fk.category_id
        FROM faq_knowledge fk
        INNER JOIN faq_robot_knowledge_joins frk ON fk.id = frk.knowledge_id
        WHERE fk.category_id IN
        <foreach collection="list" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        AND frk.source = 'original'
    </select>
    <select id="findBoundCategoryNames" resultType="com.faw.work.ais.aic.model.domain.FaqCategoryPO">
        SELECT DISTINCT c.id, c.name
        FROM faq_category c
        INNER JOIN faq_knowledge fk ON c.id = fk.category_id
        INNER JOIN faq_robot_knowledge_joins frk ON fk.id = frk.knowledge_id
        WHERE c.id IN
        <foreach collection="list" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        AND frk.source = 'original'
    </select>


</mapper>
