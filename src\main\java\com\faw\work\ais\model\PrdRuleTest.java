package com.faw.work.ais.model;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * prd_rule_test
 * <AUTHOR>
@Data
public class PrdRuleTest implements Serializable {
    private Integer id;

    /**
     * 文档中url
     */
    private String url;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 系统id
     */
    private String systemId;

    /**
     * 唯一id
     */
    private String traceid;

    private String humanResult;

    private String humanScore;

    private String aiResult;

    private String rawResult;

    /**
     * prd文档的文本
     */
    private String givenInfoJson;

    private static final long serialVersionUID = 1L;


}