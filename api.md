# LlmRecordController

LlmRecordController


---
## 处理对话

> BASIC

**Path:** /llm-record/process-conversation

**Method:** POST

**Desc:**

 处理对话，包括情绪分析和标签提取
处理对话
处理对话，包括情绪分析和标签提取

> REQUEST

**Headers:**

| name | value | required | desc |
| ------------ | ------------ | ------------ | ------------ |
| Content-Type | application/json | YES |  |

**Request Body:**

| name | type | desc |
| ------------ | ------------ | ------------ |
| requestId | string | 对话ID |
| userInput | string | 录音内容，格式如下：(start:0000) 接待专员：您好，我是一汽红旗的产品顾问，看您在网上关注我们。请问您是王鑫先生吗？ (end:5000)<br>(start:6000) 客户：是的！ (end:7000)<br>(start:8000) 客户：请问您最近有买车的需求吗？ (end:10000)<br>(start:11000) 客户：有，你们最右有什么车型有活动吗？ (end:14000)<br>(start:15000) 接待专员：我们有好几款车有活动，例如HS5，H5，EH7，天工08，请问您买车的需求是什么呢？ (end:21000)<br>(start:22000) 客户：我主要是用于城市通勤，上班下班。天工08都有啥活动？ (end:27000) |
| textUrl | string | 文本的腾讯云cos桶url |
| audioUrl | string | 语音的腾讯云cos桶url |

**Request Demo:**

```json
{
  "requestId": "",
  "userInput": "",
  "textUrl": "",
  "audioUrl": ""
}
```



> RESPONSE

**Headers:**

| name | value | required | desc |
| ------------ | ------------ | ------------ | ------------ |
| content-type | application/json;charset=UTF-8 | NO |  |

**Body:**

| name | type | desc |
| ------------ | ------------ | ------------ |
| code | integer | 响应代码 成功：200；失败：-5001 |
| message | string | 响应描述 |
| traceId | string | 唯一码，查日志使用 |
| timestamp | string | 响应的时间戳 |
| data | string | 返回数据的结构体 |

**Response Demo:**

```json
{
  "code": "200",
  "message": "请求成功",
  "traceId": "",
  "timestamp": "",
  "data": ""
}
```




---
## 重试失败记录

> BASIC

**Path:** /llm-record/retry-failed

**Method:** POST

**Desc:**

 重试失败的记录
重试失败记录
根据请求ID重试所有失败的记录

> REQUEST

**Headers:**

| name | value | required | desc |
| ------------ | ------------ | ------------ | ------------ |
| Content-Type | application/json | YES |  |

**Request Body:**

| name | type | desc |
| ------------ | ------------ | ------------ |
| requestId | array | 请求ID |
| &ensp;&ensp;&#124;─ | string |  |

**Request Demo:**

```json
{
  "requestId": [
    ""
  ]
}
```



> RESPONSE

**Headers:**

| name | value | required | desc |
| ------------ | ------------ | ------------ | ------------ |
| content-type | application/json;charset=UTF-8 | NO |  |

**Body:**

| name | type | desc |
| ------------ | ------------ | ------------ |
| code | integer | 响应代码 成功：200；失败：-5001 |
| message | string | 响应描述 |
| traceId | string | 唯一码，查日志使用 |
| timestamp | string | 响应的时间戳 |
| data | string | 返回数据的结构体 |

**Response Demo:**

```json
{
  "code": "200",
  "message": "请求成功",
  "traceId": "",
  "timestamp": "",
  "data": ""
}
```




---
## 清理已完成的消息队列

> BASIC

**Path:** /llm-record/clean-completed-messages

**Method:** POST

**Desc:**

 清理已完成的消息队列数据
清理已完成的消息队列
清理状态为3（已完成）的消息队列数据

> REQUEST



> RESPONSE

**Headers:**

| name | value | required | desc |
| ------------ | ------------ | ------------ | ------------ |
| content-type | application/json;charset=UTF-8 | NO |  |

**Body:**

| name | type | desc |
| ------------ | ------------ | ------------ |
| code | integer | 响应代码 成功：200；失败：-5001 |
| message | string | 响应描述 |
| traceId | string | 唯一码，查日志使用 |
| timestamp | string | 响应的时间戳 |
| data | object | 返回数据的结构体 |
| &ensp;&ensp;&#124;─cleanCount | integer | 清理的消息数量 |
| &ensp;&ensp;&#124;─requestIds | array | 处理的请求ID列表 |
| &ensp;&ensp;&ensp;&ensp;&#124;─ | string |  |

**Response Demo:**

```json
{
  "code": "200",
  "message": "请求成功",
  "traceId": "",
  "timestamp": "",
  "data": {
    "cleanCount": 0,
    "requestIds": [
      ""
    ]
  }
}
```



