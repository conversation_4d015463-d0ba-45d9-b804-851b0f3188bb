package com.faw.work.ais.aic.service.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.faw.work.ais.aic.config.InspectionContext;
import com.faw.work.ais.aic.model.domain.InspectionItemKeywordsPO;
import com.faw.work.ais.aic.model.domain.InspectionRulePO;
import com.faw.work.ais.aic.model.dto.MatchedItemDTO;
import com.faw.work.ais.aic.service.InspectionRuleChecker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Stream;

/**
 * 关键词和正则表达式规则检查器
 * <AUTHOR>
 */
@Component
@Slf4j
public class KeywordRule<PERSON>he<PERSON> implements InspectionRuleChecker {

    private static final String TYPE = "keyword";
    private static final String KEYWORD_TYPE_PLAIN = "00";
    private static final String KEYWORD_TYPE_REGEX = "01";
    private static final String CONDITION_ANY = "any";
    private static final String CONDITION_ALL = "all";
    private static final String CONDITION_NONE = "none";

    /**
     * {@inheritDoc}
     */
    @Override
    public List<MatchedItemDTO> check(InspectionRulePO rule, InspectionContext context) {
        List<MatchedItemDTO> results = new ArrayList<>();
        List<InspectionItemKeywordsPO> keywordItems = context.getKeywordsMap().get(rule.getId());

        if (CollUtil.isEmpty(keywordItems)) {
            log.warn("InspectionRule({}) has no keyword items", rule.getId());
            return results;
        }

        for (InspectionItemKeywordsPO item : keywordItems) {
            boolean isMatched = false;
            if (Objects.equals(KEYWORD_TYPE_PLAIN, item.getKeywordsType())) {
                isMatched = checkPlainKeywords(item, context.getCustomerServiceSpeech());
            } else if (Objects.equals(KEYWORD_TYPE_REGEX, item.getKeywordsType())) {
                isMatched = checkRegex(item, context.getCustomerServiceSpeech());
            }
            results.add(MatchedItemDTO.builder()
                    .type(TYPE)
                    .content(item.getKeywordsRule())
                    .matched(isMatched)
                    .build());
        }
        return results;
    }

    private boolean checkPlainKeywords(InspectionItemKeywordsPO item, String speech) {
        String[] keywords = StrUtil.splitToArray(item.getKeywordsRule(), '|');
        if (keywords.length == 0) {
            return false;
        }
        Stream<String> stream = Arrays.stream(keywords).filter(StrUtil::isNotBlank);
        return switch (item.getKeywordsCondition()) {
            case CONDITION_ANY -> stream.anyMatch(speech::contains);
            case CONDITION_ALL -> stream.allMatch(speech::contains);
            case CONDITION_NONE -> stream.noneMatch(speech::contains);
            default -> false;
        };
    }

    private boolean checkRegex(InspectionItemKeywordsPO item, String speech) {
        if (StrUtil.isBlank(item.getKeywordsRule())) {
            return false;
        }
        return Pattern.compile(item.getKeywordsRule()).matcher(speech).find();
    }
}

