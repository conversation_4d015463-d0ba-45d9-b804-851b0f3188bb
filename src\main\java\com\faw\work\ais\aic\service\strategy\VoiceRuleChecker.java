package com.faw.work.ais.aic.service.strategy;

import cn.hutool.core.collection.CollUtil;
import com.faw.work.ais.aic.config.InspectionContext;
import com.faw.work.ais.aic.model.domain.InspectionItemVoicePO;
import com.faw.work.ais.aic.model.domain.InspectionRulePO;
import com.faw.work.ais.aic.model.dto.MatchedItemDTO;
import com.faw.work.ais.aic.service.InspectionRuleChecker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 语音规则检查器（占位实现）
 * <AUTHOR>
 */
@Slf4j
@Component
public class VoiceRuleChecker implements InspectionRuleChecker {


    /**
     * {@inheritDoc}
     */
    @Override
    public List<MatchedItemDTO> check(InspectionRulePO rule, InspectionContext context) {
        List<InspectionItemVoicePO> voiceItems = context.getVoiceMap().get(rule.getId());
        if (CollUtil.isNotEmpty(voiceItems)) {
            log.warn("规则 {} 包含语音类检查，但当前输入为纯文本，将跳过此项检查。", rule.getId());
        }

        // 当前为纯文本质检，不处理语音规则，直接返回空列表
        return new ArrayList<>();
    }
}

