package com.faw.work.ais.aic.controller;


import com.faw.work.ais.aic.model.request.InspectionRequest;
import com.faw.work.ais.aic.model.response.InspectionResponse;
import com.faw.work.ais.aic.service.InspectionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 质检服务API
 * <AUTHOR>
 */
@RestController
@Tag(name = "质检服务", description = "提供文本质检相关功能")
@RequestMapping("/inspection")
public class InspectionController {

    @Autowired
    private InspectionService inspectionService;

    /**
     * 执行质检功能
     *
     * @param request 包含质检方案ID和客服发言内容的请求体
     * @return 质检结果，包括总分和各规则的命中详情
     */
    @PostMapping("/execute")
    @Operation(summary = "执行质检", description = "根据质检方案ID和客服发言，执行质检并返回结果")
    public InspectionResponse executeInspection(@Valid @RequestBody InspectionRequest request) {
        return inspectionService.execute(request);
    }

    /**
     * 初始化指定质检规则的相似度数据
     *
     * @param ruleId 质检规则ID
     */
    @PostMapping("/initialize-similarity")
    @Operation(summary = "初始化相似度数据", description = "根据质检规则ID初始化相似度短语并向量化存入Milvus")
    public void initializeSimilarity(@RequestParam("ruleId") Long ruleId) {
        inspectionService.initializeSimilarityData(ruleId);
    }

}

