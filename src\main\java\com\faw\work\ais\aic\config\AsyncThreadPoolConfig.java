package com.faw.work.ais.aic.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Configuration
@EnableAsync
public class AsyncThreadPoolConfig {

    @Value("${task.execution.pool.faq-publish.core-size:50}")
    private int faqPublishCorePoolSize;
    @Value("${task.execution.pool.faq-publish.max-size:100}")
    private int faqPublishMaxPoolSize;
    @Value("${task.execution.pool.faq-publish.queue-capacity:500}")
    private int faqPublishQueueCapacity;
    @Value("${task.execution.faq-publish.thread-name-prefix:task-}")
    private String faqPublishNamePrefix;
    @Value("${task.execution.faq-publish.pool.keep-alive:10}")
    private int faqPublishKeepAliveSeconds;



    @Value("${task.execution.pool.dms-emotion.core-size:20}")
    private int dmsEmotionCorePoolSize;
    @Value("${task.execution.pool.dms-emotion.max-size:100}")
    private int dmsEmotionMaxPoolSize;
    @Value("${task.execution.pool.dms-emotion.queue-capacity:1000}")
    private int dmsEmotionQueueCapacity;
    @Value("${task.execution.dms-emotion.thread-name-prefix:task-}")
    private String dmsEmotionNamePrefix;
    @Value("${task.execution.dms-emotion.pool.keep-alive:10}")
    private int dmsEmotionKeepAliveSeconds;


    @Value("${task.execution.pool.async.core-size:50}")
    private int asyncCorePoolSize;
    @Value("${task.execution.pool.async.max-size:100}")
    private int asyncMaxPoolSize;
    @Value("${task.execution.pool.async.queue-capacity:500}")
    private int asyncQueueCapacity;
    @Value("${task.execution.async.thread-name-prefix:task-}")
    private String asyncNamePrefix;
    @Value("${task.execution.async.pool.keep-alive:10}")
    private int asyncKeepAliveSeconds;

    @Value("${task.execution.pool.content-summary.core-size:50}")
    private int contentSummaryCorePoolSize;
    @Value("${task.execution.pool.content-summary.max-size:100}")
    private int contentSummaryMaxPoolSize;
    @Value("${task.execution.pool.content-summary.queue-capacity:500}")
    private int contentSummaryQueueCapacity;
    @Value("${task.execution.content-summary.thread-name-prefix:task-}")
    private String contentSummaryNamePrefix;
    @Value("${task.execution.content-summary.pool.keep-alive:10}")
    private int contentSummaryKeepAliveSeconds;



    @Value("${task.execution.pool.ai-inspection.core-size:10}")
    private int aiInspectionCorePoolSize;
    @Value("${task.execution.pool.ai-inspection.max-size:20}")
    private int aiInspectionMaxPoolSize;
    @Value("${task.execution.pool.ai-inspection.queue-capacity:200}")
    private int aiInspectionQueueCapacity;
    @Value("${task.execution.ai-inspection.thread-name-prefix:ai-inspection-}")
    private String aiInspectionNamePrefix;
    @Value("${task.execution.ai-inspection.pool.keep-alive:60}")
    private int aiInspectionKeepAliveSeconds;


    @Bean("faqPublishExecutor")
    public Executor faqPublishExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(faqPublishMaxPoolSize);
        //核心线程数
        executor.setCorePoolSize(faqPublishCorePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(faqPublishQueueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(faqPublishNamePrefix + "faq-publish-");
        //线程存活时间
        executor.setKeepAliveSeconds(faqPublishKeepAliveSeconds);
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }


    @Bean("dmsEmotionExecutor")
    public Executor dmsEmotionExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(dmsEmotionMaxPoolSize);
        //核心线程数
        executor.setCorePoolSize(dmsEmotionCorePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(dmsEmotionQueueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(dmsEmotionNamePrefix + "dms-emotion-");
        //线程存活时间
        executor.setKeepAliveSeconds(dmsEmotionKeepAliveSeconds);
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }



    @Bean("asyncExecutor")
    public Executor asyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(asyncMaxPoolSize);
        //核心线程数
        executor.setCorePoolSize(asyncCorePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(asyncQueueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(asyncNamePrefix + "async-"); // Changed prefix
        //线程存活时间
        executor.setKeepAliveSeconds(asyncKeepAliveSeconds);
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Bean("contentSummaryExecutor")
    public Executor contentSummaryExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setMaxPoolSize(contentSummaryMaxPoolSize);
        executor.setCorePoolSize(contentSummaryCorePoolSize);
        executor.setQueueCapacity(contentSummaryQueueCapacity);
        executor.setThreadNamePrefix(contentSummaryNamePrefix + "content-summary-");
        executor.setKeepAliveSeconds(contentSummaryKeepAliveSeconds);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        executor.initialize();
        return executor;
    }




    /**
     * 新增的线程池，专门用于AI并发质检任务。
     * 核心线程数不宜过大，因为AI调用是I/O密集型任务，主要瓶颈在网络和模型响应速度。
     */
    @Bean("aiQualityInspectionExecutor")
    public Executor aiQualityInspectionExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(aiInspectionCorePoolSize);
        executor.setMaxPoolSize(aiInspectionMaxPoolSize);
        executor.setQueueCapacity(aiInspectionQueueCapacity);
        executor.setThreadNamePrefix(aiInspectionNamePrefix);
        executor.setKeepAliveSeconds(aiInspectionKeepAliveSeconds);
        // 使用调用者运行策略，当线程池和队列都满时，由提交任务的线程自己来执行，避免任务丢失。
        // AbortPolicy会直接抛异常，对于高并发场景可能过于激进。
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

}