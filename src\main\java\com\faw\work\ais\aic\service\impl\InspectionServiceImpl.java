package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.faw.work.ais.aic.common.enums.EmbeddingModelTypeEnum;
import com.faw.work.ais.aic.config.InspectionContext;
import com.faw.work.ais.aic.mapper.inspection.*;
import com.faw.work.ais.aic.model.domain.*;
import com.faw.work.ais.aic.model.dto.EmbeddingPropertyDTO;
import com.faw.work.ais.aic.model.dto.MatchedItemDTO;
import com.faw.work.ais.aic.model.dto.MilvusField;
import com.faw.work.ais.aic.model.dto.RuleResultDTO;
import com.faw.work.ais.aic.model.request.InspectionRequest;
import com.faw.work.ais.aic.model.response.InspectionResponse;
import com.faw.work.ais.aic.service.EmbeddingService;
import com.faw.work.ais.aic.service.InspectionRuleChecker;
import com.faw.work.ais.aic.service.InspectionService;
import com.faw.work.ais.aic.service.MilvusService;
import com.faw.work.ais.common.exception.BizException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.faw.work.ais.aic.config.MilvusPoolConfig.DEFAULT_TENANT_CLIENT_KEY;

/**
 * 质检服务实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class InspectionServiceImpl implements InspectionService {
    private static final int INITIAL_SCORE = 100;
    private static final String MATCH_ANY = "any";
    private static final String MATCH_ALL = "all";

    private static final String MILVUS_SIMILARITY_COLLECTION = "inspection_similarity_rules";


    @Autowired
    private EmbeddingService embeddingService;
    @Autowired
    private MilvusService milvusService;

    @Autowired
    private InspectionRuleMapper inspectionRuleMapper;
    @Autowired
    private InspectionItemKeywordsMapper keywordsMapper;
    @Autowired
    private InspectionItemSimilarMapper similarMapper;
    @Autowired
    private InspectionItemNegativeMapper negativeMapper;
    @Autowired
    private InspectionItemVoiceMapper voiceMapper;
    @Autowired
    private List<InspectionRuleChecker> ruleCheckers;

    /**
     * 执行质检
     *
     * @param request 质检请求，包含方案ID和待检文本
     * @return 质检结果
     */
    @Override
    public InspectionResponse execute(InspectionRequest request) {
        log.info("开始执行质检，方案ID: {}", request.getSchemeId());
        List<InspectionRulePO> rules = inspectionRuleMapper.selectRulesBySchemeId(request.getSchemeId());

        if (CollUtil.isEmpty(rules)) {
            log.warn("质检方案ID {} 下未找到任何启用的规则", request.getSchemeId());
            throw new BizException("未找到有效的质检规则");
        }

        InspectionContext context = this.buildInspectionContext(rules, request.getCustomerServiceSpeech());

        int totalScore = INITIAL_SCORE;
        List<RuleResultDTO> ruleResults = new ArrayList<>();
        // 遍历规则，逐个检查并计算总分
        for (InspectionRulePO rule : rules) {
            List<MatchedItemDTO> matchedItems = new ArrayList<>();
            // 遍历所有质检项检查器，添加命中的质检项目
            for (InspectionRuleChecker checker : ruleCheckers) {
                matchedItems.addAll(checker.check(rule, context));
            }

            boolean isRuleMatched = isRuleConsideredMatched(rule.getRuleCondition(), matchedItems);
            if (isRuleMatched) {
                totalScore += rule.getScore();
            }

            RuleResultDTO resultDTO = RuleResultDTO.builder()
                    .ruleId(rule.getId())
                    .ruleName(rule.getRuleName())
                    .score(isRuleMatched ? rule.getScore() : 0)
                    .isMatched(isRuleMatched)
                    .matchedItems(matchedItems.stream().filter(MatchedItemDTO::getMatched).collect(Collectors.toList()))
                    .build();
            ruleResults.add(resultDTO);
        }

        InspectionResponse response = new InspectionResponse();
        response.setTotalScore(totalScore);
        response.setRules(ruleResults);
        log.info("质检执行完毕，方案ID: {}, 最终得分: {}", request.getSchemeId(), totalScore);
        return response;
    }

    @Override
    public void initializeSimilarityData(Long inspectionRuleId) {
        log.info("开始为质检规则 {} 初始化相似度规则数据到Milvus集合 '{}'...", inspectionRuleId, MILVUS_SIMILARITY_COLLECTION);

        // 1. 初始化前，先删除该质检规则ID下的所有历史数据
        try {
            String filter = "inspection_rule_id == " + inspectionRuleId;
            log.info("准备从Milvus中删除质检规则ID为 {} 的历史数据，过滤器: '{}'", inspectionRuleId, filter);
            long deletedCount = milvusService.deleteByFilter(DEFAULT_TENANT_CLIENT_KEY, MILVUS_SIMILARITY_COLLECTION, filter);
            log.info("成功从Milvus中删除了 {} 条关于质检规则ID {} 的历史数据。", deletedCount, inspectionRuleId);

            // 为确保删除操作对后续的插入可见，执行 flush 操作
            if (deletedCount > 0) {
                milvusService.flushAndLoad(DEFAULT_TENANT_CLIENT_KEY, MILVUS_SIMILARITY_COLLECTION);
                log.info("为确保数据一致性，已对集合'{}'执行flush和load操作。", MILVUS_SIMILARITY_COLLECTION);
            }
        } catch (Exception e) {
            log.error("初始化前，删除Milvus中质检规则ID {} 的历史数据失败，将终止本次初始化。错误: {}", inspectionRuleId, e.getMessage(), e);
            // 抛出业务异常以中断执行并向上层报告错误
            throw new BizException("初始化相似度数据失败：无法清理历史数据。");
        }

        // 2. 从数据库查询需要初始化的数据
        List<InspectionItemSimilarPO> similarItems = similarMapper.selectList(
                new LambdaQueryWrapper<InspectionItemSimilarPO>()
                        .eq(InspectionItemSimilarPO::getStatus, 1)
                        .eq(InspectionItemSimilarPO::getInspectionRuleId, inspectionRuleId)
                        .isNotNull(InspectionItemSimilarPO::getSimilarContent)
                        .ne(InspectionItemSimilarPO::getSimilarContent, "")
        );

        if (CollUtil.isEmpty(similarItems)) {
            log.info("质检规则 {} 中没有找到启用的、且包含内容的相似度规则项，无需初始化。", inspectionRuleId);
            return;
        }

        log.info("从质检规则 {} 发现 {} 条启用的相似度规则项，准备进行拆分和向量化处理。", inspectionRuleId, similarItems.size());

        // 3. 拆分所有文本并收集元数据
        List<String> textsToEmbed = new ArrayList<>();
        List<Map<String, Object>> metadataList = new ArrayList<>();

        for (InspectionItemSimilarPO item : similarItems) {
            String[] phrases = item.getSimilarContent().split("\\|");
            int phraseIndex = 0;
            for (String phrase : phrases) {
                String trimmedPhrase = phrase.trim();
                if (StrUtil.isBlank(trimmedPhrase)) {
                    continue;
                }

                textsToEmbed.add(trimmedPhrase);

                Map<String, Object> metadata = new HashMap<>();
                // 生成唯一的向量ID，格式为：{原始表ID}-{短语索引}，确保Upsert的幂等性
                metadata.put("vectorId", item.getId() + "-" + phraseIndex);
                metadata.put("content", trimmedPhrase);
                metadata.put("inspection_rule_id", item.getInspectionRuleId());
                // 关键：存储原始的相似度规则项ID，用于后续精确召回
                metadata.put("inspection_item_id", item.getId());
                metadataList.add(metadata);

                phraseIndex++;
            }
        }

        if (textsToEmbed.isEmpty()) {
            log.info("质检规则 {} 的所有规则项内容拆分后均为空，无需初始化。", inspectionRuleId);
            return;
        }

        log.info("质检规则 {} 共拆分出 {} 条独立的相似度短语，准备进行批量向量化。", inspectionRuleId, textsToEmbed.size());
        List<MilvusRow> rowsToUpsert = new ArrayList<>();

        // 4. 批量向量化并构建 MilvusRow
        final int embeddingBatchSize = 100;
        List<List<String>> textBatches = Lists.partition(textsToEmbed, embeddingBatchSize);
        List<List<Map<String, Object>>> metadataBatches = Lists.partition(metadataList, embeddingBatchSize);

        for (int i = 0; i < textBatches.size(); i++) {
            List<String> textBatch = textBatches.get(i);
            List<Map<String, Object>> metadataBatch = metadataBatches.get(i);

            log.info("正在处理质检规则 {} 的向量化批次 {}/{}，包含 {} 条文本。", inspectionRuleId, i + 1, textBatches.size(), textBatch.size());
            List<EmbeddingPropertyDTO> embeddings = embeddingService.getEmbeddingList(EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3, textBatch);

            if (CollUtil.isEmpty(embeddings) || embeddings.size() != textBatch.size()) {
                log.error("Embedding服务返回结果异常：请求 {} 个文本，返回 {} 个向量。跳过此批次。", textBatch.size(), CollUtil.size(embeddings));
                continue;
            }

            for (int j = 0; j < embeddings.size(); j++) {
                EmbeddingPropertyDTO embedding = embeddings.get(j);
                Map<String, Object> metadata = metadataBatch.get(j);

                if (embedding != null && embedding.getEmbedding() != null) {
                    List<MilvusField> properties = MilvusField.buildProperties(
                            "content", metadata.get("content"),
                            "inspection_rule_id", metadata.get("inspection_rule_id"),
                            "inspection_item_id", metadata.get("inspection_item_id")
                    );

                    MilvusRow row = MilvusRow.builder()
                            .vectorId((String) metadata.get("vectorId"))
                            .embedding(embedding.getEmbedding())
                            .properties(properties)
                            .build();
                    rowsToUpsert.add(row);
                }
            }
        }

        // 5. 批量写入 Milvus
        if (CollUtil.isNotEmpty(rowsToUpsert)) {
            log.info("质检规则 {} 向量化完成，准备向Milvus upsert共 {} 条记录。", inspectionRuleId, rowsToUpsert.size());
            final int milvusBatchSize = 100;
            List<List<MilvusRow>> milvusBatches = Lists.partition(rowsToUpsert, milvusBatchSize);
            int totalProcessedCount = 0;

            for (List<MilvusRow> milvusBatch : milvusBatches) {
                milvusService.saveOrUpdateBatch(DEFAULT_TENANT_CLIENT_KEY, MILVUS_SIMILARITY_COLLECTION, milvusBatch);
                totalProcessedCount += milvusBatch.size();
                log.info("成功向Milvus upsert了 {} 条数据。", milvusBatch.size());
            }
            log.info("质检规则 {} 的相似度规则数据初始化完成。共处理了 {} 条记录。", inspectionRuleId, totalProcessedCount);
        } else {
            log.warn("质检规则 {} 没有生成任何有效的向量数据用于初始化。", inspectionRuleId);
        }
    }




    /**
     * 构建质检上下文，预先加载所有规则项以避免循环查询
     * @param rules 规则列表
     * @param speech 待检文本
     * @return 质检上下文
     */
    private InspectionContext buildInspectionContext(List<InspectionRulePO> rules, String speech) {
        List<Integer> ruleIds = rules.stream().map(InspectionRulePO::getId).collect(Collectors.toList());

        // 查询每一个质检规则对应的质检项
        Map<Integer, List<InspectionItemKeywordsPO>> keywordsMap = keywordsMapper.selectList(
                        new LambdaQueryWrapper<InspectionItemKeywordsPO>()
                                .in(InspectionItemKeywordsPO::getInspectionRuleId, ruleIds)
                                .eq(InspectionItemKeywordsPO::getStatus, 1))
                .stream().collect(Collectors.groupingBy(item -> item.getInspectionRuleId().intValue()));

        Map<Integer, List<InspectionItemSimilarPO>> similarMap = similarMapper.selectList(
                        new LambdaQueryWrapper<InspectionItemSimilarPO>()
                                .in(InspectionItemSimilarPO::getInspectionRuleId, ruleIds)
                                .eq(InspectionItemSimilarPO::getStatus, 1))
                .stream().collect(Collectors.groupingBy(item -> item.getInspectionRuleId().intValue()));

        Map<Integer, List<InspectionItemNegativePO>> negativeMap = negativeMapper.selectList(
                        new LambdaQueryWrapper<InspectionItemNegativePO>()
                                .in(InspectionItemNegativePO::getInspectionRuleId, ruleIds)
                                .eq(InspectionItemNegativePO::getStatus, 1))
                .stream().collect(Collectors.groupingBy(item -> item.getInspectionRuleId().intValue()));

        Map<Integer, List<InspectionItemVoicePO>> voiceMap = voiceMapper.selectList(
                        new LambdaQueryWrapper<InspectionItemVoicePO>()
                                .in(InspectionItemVoicePO::getInspectionRuleId, ruleIds)
                                .eq(InspectionItemVoicePO::getStatus, 1))
                .stream().collect(Collectors.groupingBy(item -> item.getInspectionRuleId().intValue()));

        // 获取客服发言的向量
        List<EmbeddingPropertyDTO> embeddingList = embeddingService.getEmbeddingList(EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3, Collections.singletonList(speech));

        return new InspectionContext(speech, embeddingList.get(0), keywordsMap, similarMap, negativeMap, voiceMap);
    }

    /**
     * 根据规则的匹配条件（any/all）和所有子项的匹配结果，判断该规则是否最终被视为“命中”
     * @param ruleCondition 规则的匹配条件 ("any", "all")
     * @param matchedItems 所有质检子项的匹配结果列表
     * @return 如果规则被视为命中，则返回true
     */
    private boolean isRuleConsideredMatched(String ruleCondition, List<MatchedItemDTO> matchedItems) {
        if (CollUtil.isEmpty(matchedItems)) {
            return false;
        }

        if (Objects.equals(MATCH_ANY, ruleCondition)) {
            return matchedItems.stream().anyMatch(MatchedItemDTO::getMatched);
        } else if (Objects.equals(MATCH_ALL, ruleCondition)) {
            return matchedItems.stream().allMatch(MatchedItemDTO::getMatched);
        }
        return false;
    }
}
