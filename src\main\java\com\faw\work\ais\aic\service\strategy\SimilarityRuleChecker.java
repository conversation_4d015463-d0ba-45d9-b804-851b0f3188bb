package com.faw.work.ais.aic.service.strategy;

import cn.hutool.core.collection.CollUtil;
import com.faw.work.ais.aic.config.InspectionContext;
import com.faw.work.ais.aic.config.MilvusPoolConfig;
import com.faw.work.ais.aic.model.domain.InspectionItemSimilarPO;
import com.faw.work.ais.aic.model.domain.InspectionRulePO;
import com.faw.work.ais.aic.model.dto.EmbeddingPropertyDTO;
import com.faw.work.ais.aic.model.dto.MatchedItemDTO;
import com.faw.work.ais.aic.model.dto.VectorSearchResult;
import com.faw.work.ais.aic.service.EmbeddingService;
import com.faw.work.ais.aic.service.InspectionRuleChecker;
import com.faw.work.ais.aic.service.MilvusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 相似话术规则检查器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SimilarityRuleChecker implements InspectionRuleChecker {

    private static final String TYPE = "similar";
    private static final int SEARCH_TOP_K = 1;

    @Autowired
    @Qualifier("aiQualityInspectionExecutor")
    private Executor aiQualityInspectionExecutor;

    @Autowired
    private MilvusService milvusService;

    @Autowired
    private EmbeddingService embeddingService;

    /**
     * {@inheritDoc}
     * <p>
     * 检查一个规则下的所有相似度项。
     * 使用CompletableFuture并发处理每个相似度项，提高检查性能。
     * 只要有任意一个相似度项匹配成功，则认为该规则的"相似度"部分被命中，并返回一个单一的、为true的匹配结果。
     * 如果所有相似度项都未匹配，则返回一个单一的、为false的匹配结果。
     * 如果规则下没有任何相似度项，则返回一个空列表。
     */
    @Override
    public List<MatchedItemDTO> check(InspectionRulePO rule, InspectionContext context) {
        List<InspectionItemSimilarPO> similarItems = context.getSimilarMap().get(rule.getId());

        if (CollUtil.isEmpty(similarItems)) {
            return Collections.emptyList();
        }

        EmbeddingPropertyDTO speechEmbedding = context.getSpeechEmbedding();
        if (speechEmbedding == null || speechEmbedding.getEmbedding() == null || speechEmbedding.getEmbedding().length == 0) {
            log.warn("无法获取待检文本的向量，跳过规则 {} 的相似度检查", rule.getId());
            return Collections.emptyList();
        }
        float[] speechVector = speechEmbedding.getEmbedding();

        // 创建CompletableFuture列表，并发处理每个相似度项
        List<CompletableFuture<MatchedItemDTO>> futures = similarItems.stream()
                .map(item -> CompletableFuture.supplyAsync(() -> checkSimilarityItem(item, rule, context, speechVector), aiQualityInspectionExecutor)).toList();

        // 等待所有任务完成并收集结果
        List<MatchedItemDTO> results = futures.stream()
                .map(CompletableFuture::join).toList();

        // 检查是否有任何匹配项
        for (MatchedItemDTO result : results) {
            if (result != null && result.getMatched()) {
                log.info("规则ID {} 的相似度检查命中", rule.getId());
                return Collections.singletonList(result);
            }
        }

        // 如果没有找到任何匹配项，则表明该规则的相似度部分未命中
        log.debug("规则ID {} 的相似度检查未命中，所有相似项均未达到阈值。", rule.getId());
        return Collections.singletonList(MatchedItemDTO.builder()
                .type(TYPE)
                .content("无相似内容命中")
                .matched(false)
                .build());
    }

    /**
     * 检查单个相似度项
     *
     * @param item 相似度项
     * @param rule 规则
     * @param context 检查上下文
     * @param speechVector 语音向量
     * @return 匹配结果，如果未匹配则返回null
     */
    private MatchedItemDTO checkSimilarityItem(InspectionItemSimilarPO item, InspectionRulePO rule,
                                               InspectionContext context, float[] speechVector) {
        try {
            String filter = "inspection_item_id == " + item.getId();

            List<VectorSearchResult> searchResults = milvusService.searchByEmbedding(
                    MilvusPoolConfig.INSPECTION_SIMILARITY_RULES,
                    speechVector,
                    5,
                    item.getSimilarityThreshold(),
                    filter
            );

            if (searchResults.isEmpty()) {
                log.warn("相似度检查未命中，未找到相似项。规则ID:{},相似项ID:{},相似度阈值:{}",
                        rule.getId(), item.getId(), item.getSimilarityThreshold());
                return null;
            }
            log.info("规则ID {} 的相似度检查命中。原因：输入文本与相似项ID {} (阈值: {}) 匹配成功。匹配内容: '{}',embedding匹配相似度: {}",
                    rule.getId(), item.getId(), item.getSimilarityThreshold(), searchResults.get(0).getContent(),
                    searchResults.get(0).getScore());

            String matchedContent = "相似内容命中: " + searchResults.get(0).getContent();
            return MatchedItemDTO.builder()
                    .type(TYPE)
                    .content(matchedContent)
                    .matched(true)
                    .build();
            // RerankResponse rerankResponse = embeddingService.getReRankResult(
            //         new RerankRequest(
            //                 context.getCustomerServiceSpeech(),
            //                 List.of(new Document(searchResults.get(0).getContent())),
            //                 DashScopeRerankOptions.builder().withTopN(SEARCH_TOP_K).build()
            //         )
            // );
            //
            // Double score = rerankResponse.getResult().getScore();

            // if (score >= item.getSimilarityThreshold()) {
            //     log.info("规则ID {} 的相似度检查命中。原因：输入文本与相似项ID {} (阈值: {}) 匹配成功。匹配内容: '{}',embedding匹配相似度: {},重排序:{}",
            //             rule.getId(), item.getId(), item.getSimilarityThreshold(), searchResults.get(0).getContent(),
            //             searchResults.get(0).getScore(), score);
            //
            //     String matchedContent = "相似内容命中: " + searchResults.get(0).getContent();
            //     return MatchedItemDTO.builder()
            //             .type(TYPE)
            //             .content(matchedContent)
            //             .matched(true)
            //             .build();
            // } else {
            //     log.info("规则ID {} 的相似度检查未命中。原因：输入文本与相似项ID {} (阈值: {}) 匹配失败。匹配内容: '{}',embedding匹配相似度: {},重排序:{}",
            //             rule.getId(), item.getId(), item.getSimilarityThreshold(), searchResults.get(0).getContent(),
            //             searchResults.get(0).getScore(), score);
            //     return null;
            // }

        } catch (Exception e) {
            log.error("检查相似度项时发生异常。规则ID:{},相似项ID:{}", rule.getId(), item.getId(), e);
            return null;
        }
    }
}
