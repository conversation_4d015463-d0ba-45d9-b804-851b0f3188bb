package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.common.enums.EnvEnum;
import com.faw.work.ais.aic.mapper.faq.*;
import com.faw.work.ais.aic.model.domain.*;
import com.faw.work.ais.aic.model.request.FaqRobotKnowledgeRequest;
import com.faw.work.ais.aic.model.request.FaqRobotQueryRequest;
import com.faw.work.ais.aic.model.request.FaqRobotSubmitRequest;
import com.faw.work.ais.aic.model.response.FaqRobotResponse;
import com.faw.work.ais.aic.service.*;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.common.util.DateUtils;
import com.faw.work.ais.common.util.UserThreadLocalUtil;
import com.faw.work.ais.aic.config.MilvusPoolConfig;
import com.faw.work.ais.aic.model.dto.AutoEmbeddingDTO;
import com.faw.work.ais.aic.model.dto.VectorSearchResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * FAQ机器人Service实现类
 */
@Service
@Slf4j
public class FaqRobotServiceImpl extends ServiceImpl<FaqRobotMapper, FaqRobotPO> implements FaqRobotService {

    @Autowired
    private Executor faqPublishExecutor;
    @Autowired
    private FaqKnowledgeMapper faqKnowledgeMapper;
    @Autowired
    private MilvusService milvusService;
    @Autowired
    FaqSimilarKnowledgeMapper faqSimilarKnowledgeMapper;

    @Autowired
    FaqSimilarKnowledgeProdMapper faqSimilarKnowledgeProdMapper;
    @Autowired
    FaqCategoryMapper faqCategoryMapper;
    @Autowired
    private FaqRobotKnowledgeJoinsMapper faqRobotKnowledgeJoinsMapper;
    @Autowired
    private FaqRobotMapper faqRobotMapper;
    @Autowired
    private FaqKnowledgeService faqKnowledgeService;

    @Autowired
    private FaqKnowledgeProdService faqKnowledgeProdService;

    @Autowired
    private FaqRobotService faqRobotService;

    @Autowired
    private FaqCategoryService faqCategoryService;

    @Autowired
    private FaqRobotKnowledgeJoinsService faqRobotKnowledgeJoinsService;
    @Autowired
    private FaqRobotKnowledgeJoinsProdService faqRobotKnowledgeJoinsProdService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createRobot(FaqRobotSubmitRequest request) {
        // 新建机器人
        String robotName = request.getRobotName();
        FaqRobotPO oldRobot = faqRobotMapper.getRobotByName(robotName);
        if (oldRobot != null) {
            throw new BizException("机器人名称已存在");
        }
        FaqRobotPO robot = new FaqRobotPO();
        robot.setRobotName(request.getRobotName());
        robot.setStatus(0);
        robot.setCreatedBy(UserThreadLocalUtil.getRealName());
        robot.setCreatedAt(LocalDateTime.now());
        robot.setVersion(0);
        this.save(robot);
        // 绑定知识库
        List<String> categoryIds = request.getCategoryId();

        if (CollUtil.isNotEmpty(categoryIds)) {
            int count = faqKnowledgeMapper.selectDistinctCountByCategoryId(categoryIds);
            if (count != categoryIds.size()) {
                throw new BizException("未新建知识的类目不允许绑定机器人");
            }
            faqRobotService.resetBindKnowledgeJoinsAndMilvusByCategoryIds(new FaqRobotKnowledgeRequest(robot.getId(), categoryIds));
        }
        return robot.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class,isolation = Isolation.READ_UNCOMMITTED)
    public void updateRobot(FaqRobotSubmitRequest request) {
        FaqRobotPO robot = getById(request.getId());
        if (robot == null) {
            throw new IllegalArgumentException("机器人不存在");
        }

        // 检查机器人状态，如果正在绑定中则抛出异常
        if (robot.getStatus() == 5) {
            throw new BizException("机器人正在绑定知识库中，请稍后重试");
        }

        robot.setRobotName(request.getRobotName());
        robot.setUpdatedAt(LocalDateTime.now());
        robot.setUpdatedBy(UserThreadLocalUtil.getRealName());

        List<String> categoryIds = request.getCategoryId();
        if (CollUtil.isNotEmpty(categoryIds)) {
            int count = faqKnowledgeMapper.selectDistinctCountByCategoryId(categoryIds);
            if (count != categoryIds.size()) {
                throw new BizException("未新建知识的类目不允许绑定机器人");
            }

            // 设置状态为绑定中
            robot.setStatus(5);
            this.updateById(robot);

            // 异步绑定知识库
            CompletableFuture.runAsync(() -> {
                try {
                    // 绑定知识库
                    faqRobotService.resetBindKnowledgeJoinsAndMilvusByCategoryIds(FaqRobotKnowledgeRequest.builder()
                            .robotId(robot.getId())
                            .categoryId(categoryIds)
                            .build());

                    // 绑定成功，更新状态为0
                    FaqRobotPO updateRobot = new FaqRobotPO();
                    updateRobot.setId(robot.getId());
                    updateRobot.setStatus(0);
                    updateRobot.setUpdatedAt(LocalDateTime.now());
                    updateRobot.setUpdatedBy(UserThreadLocalUtil.getRealName());
                    this.updateById(updateRobot);

                } catch (Exception e) {
                    // 绑定失败，更新状态为0，但不保存类目
                    FaqRobotPO updateRobot = new FaqRobotPO();
                    updateRobot.setId(robot.getId());
                    updateRobot.setStatus(0);
                    updateRobot.setUpdatedAt(LocalDateTime.now());
                    updateRobot.setUpdatedBy(UserThreadLocalUtil.getRealName());
                    this.updateById(updateRobot);

                    // 记录异常日志
                    log.error("机器人绑定知识库失败，robotId: {}, categoryIds: {}", robot.getId(), categoryIds, e);
                }
            }, faqPublishExecutor);

        } else {
            // 解绑知识库 - 同步执行
            robot.setStatus(0);
            this.updateById(robot);

            List<FaqRobotKnowledgeJoinsPO> faqRobotKnowledgeJoinsPOS = faqRobotKnowledgeJoinsService.selectAllByRobotId(robot.getId());
            List<String> ids = faqRobotKnowledgeJoinsPOS.stream().map(FaqRobotKnowledgeJoinsPO::getId).toList();
            faqRobotKnowledgeJoinsService.removeByIds(ids);
            milvusService.deleteByIds(MilvusPoolConfig.FAQ_COLLECTION_NAME, ids, null);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRobot(String id) {
        return removeById(id);
    }

    @Override
    public FaqRobotResponse getRobotDetail(String robotId, String env) {

        FaqRobotPO robotPO = getById(robotId);
        List<FaqCategoryPO> categoryPOList;

        if (EnvEnum.TEST.getCode().equals(env)) {

            List<String> categoryIds = faqRobotKnowledgeJoinsMapper.selectCategoryIdsByRobotId(robotId);
            categoryPOList = new ArrayList<>();
            if (CollUtil.isNotEmpty(categoryIds)) {
                categoryPOList = faqCategoryService.lambdaQuery()
                        .in(FaqCategoryPO::getId, categoryIds)
                        .list();
            }

        } else if (EnvEnum.PROD.getCode().equals(env)) {
            List<String> categoryIds = faqRobotKnowledgeJoinsMapper.selectProdCategoryIdsByRobotId(robotId);
            categoryPOList = new ArrayList<>();
            if (CollUtil.isNotEmpty(categoryIds)) {
                categoryPOList = faqCategoryMapper.selectProdCategoryListByIds(categoryIds);
            }
        } else {
            throw new BizException("环境类型不正确");
        }
        FaqRobotResponse response = new FaqRobotResponse();
        if(robotPO != null){
            BeanUtils.copyProperties(robotPO, response);
            response.setCategoryList(categoryPOList);
        }
        return response;


    }

    @Override
    public IPage<FaqRobotPO> pageRobot(FaqRobotQueryRequest request) {
        Page<FaqRobotPO> page = new Page<>(request.getPageNum(), request.getPageSize());
        return baseMapper.selectPageWithRobotName(page, request.getRobotName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(String id, Integer status) {
        FaqRobotPO robot = getById(id);
        if (robot == null) {
            return false;
        }
        robot.setStatus(status);
        robot.setUpdatedAt(LocalDateTime.now());
        robot.setUpdatedBy(UserThreadLocalUtil.getRealName());
        return updateById(robot);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,isolation = Isolation.READ_UNCOMMITTED)
    public boolean publishRobot(String robotId) throws InterruptedException {
        // 更新机器人信息
        FaqRobotPO robot = getById(robotId);
        if (2 == robot.getStatus()) {
            throw new BizException("机器人正在发布中，请勿重复发布");
        }
        if (5 == robot.getStatus()) {
            throw new BizException("机器人正在绑定知识中，请稍后重试");
        }
        robot.setPublishName(UserThreadLocalUtil.getRealName());
        robot.setPublishTime(DateUtils.formatDate(new Date(), DateUtils.FORMAT_DATE_TIME));
        robot.setStatus(2);
        robot.setUpdatedAt(LocalDateTime.now());
        robot.setUpdatedBy(UserThreadLocalUtil.getRealName());
        Integer version = robot.getVersion();
        if (version == null) {
            version = 1;
        } else {
            version++;
        }
        robot.setVersion(version);
        this.updateById(robot);
        // 保存发布记录
        FaqRobotPublishPO FaqRobotPublishPO = new FaqRobotPublishPO();
        FaqRobotPublishPO.setRobotId(robotId);
        FaqRobotPublishPO.setPublisher(UserThreadLocalUtil.getRealName());
        FaqRobotPublishPO.setPublishTime(LocalDateTime.now());
        FaqRobotPublishPO.setStatus("1");
        FaqRobotPublishPO.setVersion(version);
        faqRobotMapper.savePublishRecord(FaqRobotPublishPO);
        // 核心方法：数据迁移到生产环境
        faqRobotKnowledgeJoinsService.migrateAllData(List.of(robotId));
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetBindKnowledgeJoinsAndMilvusByCategoryIds(FaqRobotKnowledgeRequest request) {
        String robotId = request.getRobotId();
        List<String> categoryIdList = request.getCategoryId();
        if (robotId == null || CollUtil.isEmpty(categoryIdList)) {
            throw new BizException("类目ID或机器人ID参数不能为空");
        }

        // 1. 获取当前机器人已关联的所有知识
        List<FaqRobotKnowledgeJoinsPO> existingJoins = faqRobotKnowledgeJoinsService.lambdaQuery()
                .eq(FaqRobotKnowledgeJoinsPO::getRobotId, robotId)
                .list();

        // 2. 根据类目查询所有符合条件的原始知识和相似问题
        // 2.0 准备插入中间表的数据集合
        List<FaqRobotKnowledgeJoinsPO> newJoinsList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        String realName = UserThreadLocalUtil.getRealName();

        // 2.1 查询所有符合条件的原始知识
        List<String> originalKnowledgeIds = faqKnowledgeMapper.selectKnowledgeIdsByCategoryIds(categoryIdList);

        Map<String, String> knowledgeIdWithQuestionMap = new HashMap<>();
        if (CollUtil.isNotEmpty(originalKnowledgeIds)) {
            // 3. 准备新的关联集合

            // 3.1 添加原始知识到准备插入的关联数据集合
            List<FaqKnowledgePO> knowledgeList = faqKnowledgeMapper.selectIdAndQuestionByIds(originalKnowledgeIds);
            for (FaqKnowledgePO knowledge : knowledgeList) {

                knowledgeIdWithQuestionMap.put(knowledge.getId(), knowledge.getQuestion());

                FaqRobotKnowledgeJoinsPO joinsPO = new FaqRobotKnowledgeJoinsPO();
                joinsPO.setRobotId(robotId);
                joinsPO.setKnowledgeId(knowledge.getId());
                joinsPO.setSource("original");
                joinsPO.setCreatedAt(now);
                joinsPO.setCreatedBy(realName);
                joinsPO.setVectorStatus("02");
                newJoinsList.add(joinsPO);
            }

            // 3.2 添加相似问关联

            List<FaqSimilarKnowledgePO> similarKnowledgeList = faqSimilarKnowledgeMapper.selectIdAndQuestionByIds(originalKnowledgeIds);
            for (FaqSimilarKnowledgePO similarKnowledge : similarKnowledgeList) {
                knowledgeIdWithQuestionMap.put(similarKnowledge.getId(), similarKnowledge.getSimilarQuestion());

                FaqRobotKnowledgeJoinsPO joinsPO = new FaqRobotKnowledgeJoinsPO();
                joinsPO.setRobotId(robotId);
                joinsPO.setKnowledgeId(similarKnowledge.getId());
                joinsPO.setSource("similar");
                joinsPO.setCreatedAt(now);
                joinsPO.setCreatedBy(realName);
                joinsPO.setVectorStatus("02");
                newJoinsList.add(joinsPO);
            }

        }

        // 4. ----------------分析出需要新增的，需要删除，需要修改的关联关系-------------------------
        // 4.1 构建现有关联Map，key为知识ID
        Map<String, FaqRobotKnowledgeJoinsPO> existingJoinsMap = existingJoins.stream()
                .collect(Collectors.toMap(FaqRobotKnowledgeJoinsPO::getKnowledgeId, join -> join, (a, b) -> a));

        // 4.2 构建新关联Map，key为知识ID
        Map<String, FaqRobotKnowledgeJoinsPO> newJoinsMap = newJoinsList.stream()
                .collect(Collectors.toMap(FaqRobotKnowledgeJoinsPO::getKnowledgeId, join -> join, (a, b) -> a));


        // 4.5 找出question是否有变化的关联并处理
        List<String> joinsToChangedIds = this.handleChangedQuestionJoins(existingJoins, newJoinsMap, knowledgeIdWithQuestionMap);

        // 4.4 找出需要删除的关联
        List<String> joinsToDeleteIds = handleDeletedJoins(robotId, existingJoins, newJoinsMap);


        // 4.3 找出需要新增的关联
        List<String> joinsToAdd = handleAddJoins(robotId, newJoinsList, knowledgeIdWithQuestionMap, existingJoinsMap);

        log.info("机器人知识关联更新完成, robotId={}, 新增数量={}, 删除数量={}, 修改数量={}, 保留数量={}",
                robotId, joinsToAdd.size(), joinsToDeleteIds.size(), joinsToChangedIds.size(),
                newJoinsList.size() - joinsToAdd.size());

        log.info("数据一致性检查 - robotId: {}", robotId);
        log.info("总join数量: {}", newJoinsList.size());
        log.info("预期milvus数据量: {}", newJoinsList.size());

    }

    @Override
    public void syncTestRobotKnowledge(List<String> robotIds) {
        for (String robotId : robotIds) {
            List<String> categoryIds = faqRobotKnowledgeJoinsMapper.selectCategoryIdsByRobotId(robotId);
            // 4. 为每个机器人重新创建知识绑定
            this.resetBindKnowledgeJoinsAndMilvusByCategoryIds(FaqRobotKnowledgeRequest.builder()
                    .robotId(robotId)
                    .categoryId(categoryIds)
                    .build());

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByRobotId(String robotId) {
        // 先查询该机器人关联的所有知识ID
        List<String> testJoinIds = faqRobotKnowledgeJoinsMapper.selectIdsByRobotId(robotId);
        List<String> prodJoinIds = faqRobotKnowledgeJoinsMapper.selectProdIdsByRobotId(robotId);

        // 删除中间表数据
        boolean testJoinDelete = faqRobotKnowledgeJoinsMapper.deleteByRobotId(robotId) >= 0;
        boolean prodJoinDelete = faqRobotKnowledgeJoinsMapper.deleteProdByRobotId(robotId) >= 0;

        // 删除向量库中的数据
        if (testJoinDelete && !testJoinIds.isEmpty() && prodJoinDelete && !prodJoinIds.isEmpty()) {

            milvusService.deleteByIds(MilvusPoolConfig.FAQ_COLLECTION_NAME, testJoinIds, null);
            log.info("删除测试环境机器人关联知识向量成功, robotId={}, 删除数量={}", robotId, testJoinIds.size());

            milvusService.deleteByIds(MilvusPoolConfig.FAQ_COLLECTION_NAME_PROD, prodJoinIds, null);
            log.info("删除生产环境机器人关联知识向量成功, robotId={}, 删除数量={}", robotId, prodJoinIds.size());
        }

    }


    private List<String> handleAddJoins(String robotId, List<FaqRobotKnowledgeJoinsPO> newJoinsList, Map<String, String> knowledgeIdWithQuestionMap, Map<String, FaqRobotKnowledgeJoinsPO> existingJoinsMap) {
        List<FaqRobotKnowledgeJoinsPO> joinsToAdd = newJoinsList.stream()
                .filter(join -> !existingJoinsMap.containsKey(join.getKnowledgeId()))
                .collect(Collectors.toList());
        // 6. 执行新增操作
        if (!CollUtil.isEmpty(joinsToAdd)) {
            // 6.1 保存关联关系到中间表
            boolean saveResult = faqRobotKnowledgeJoinsService.saveBatch(joinsToAdd);

            // 6.2 向量化新增的关联
            if (saveResult) {
                List<AutoEmbeddingDTO> dataList = joinsToAdd.stream()
                        .map(joinPO -> {
                            String question = knowledgeIdWithQuestionMap.get(joinPO.getKnowledgeId());
                            if (question != null) {
                                return AutoEmbeddingDTO.create(
                                        joinPO.getId(),  // 使用joinId作为向量ID
                                        question,        // 使用缓存中的问题
                                        joinPO.getRobotId()
                                );
                            }
                            return null;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                // 执行向量化 - 分片处理
                if (!CollUtil.isEmpty(dataList)) {
                    final int BATCH_SIZE = 500;
                    final int totalSize = dataList.size();

                    log.info("开始向量化处理, robotId={}, 总数量={}", robotId, totalSize);

                    for (int i = 0; i < totalSize; i += BATCH_SIZE) {
                        int endIndex = Math.min(i + BATCH_SIZE, totalSize);
                        List<AutoEmbeddingDTO> batchData = dataList.subList(i, endIndex);

                        milvusService.autoEmbeddingAndInsertMilvus(MilvusPoolConfig.FAQ_COLLECTION_NAME, batchData);
                        log.info("批次处理成功, robotId={}, 当前批次数量={}, 进度={}/{}",
                                robotId, batchData.size(), endIndex, totalSize);
                    }

                    log.info("机器人知识关联新增完成, robotId={}, 总数量={}", robotId, totalSize);
                }
            }
        }
        return joinsToAdd.stream().map(FaqRobotKnowledgeJoinsPO::getId).toList();
    }


    private List<String> handleDeletedJoins(String robotId, List<FaqRobotKnowledgeJoinsPO> existingJoins, Map<String, FaqRobotKnowledgeJoinsPO> newJoinsMap) {
        List<String> joinsToDeleteIds = existingJoins.stream()
                .filter(join -> !newJoinsMap.containsKey(join.getKnowledgeId()))
                .map(FaqRobotKnowledgeJoinsPO::getId).toList();
        // 5. 执行删除操作
        if (!joinsToDeleteIds.isEmpty()) {
            // 5.1 删除中间表关联
            faqRobotKnowledgeJoinsService.removeByIds(joinsToDeleteIds);

            // 5.2 删除向量库数据
            milvusService.deleteByIds(MilvusPoolConfig.FAQ_COLLECTION_NAME, joinsToDeleteIds, null);
            log.info("删除机器人关联知识向量成功, robotId={}, 删除数量={}", robotId, joinsToDeleteIds.size());
        }
        return joinsToDeleteIds;
    }

    private List<String> handleChangedQuestionJoins(List<FaqRobotKnowledgeJoinsPO> existingJoins,
                                                    Map<String, FaqRobotKnowledgeJoinsPO> newJoinsMap,
                                                    Map<String, String> knowledgeIdWithQuestionMap) { // 新增参数

        // 1. 准备需要检查的关联ID
        List<String> joinIdsToCheck = existingJoins.stream()
                .filter(join -> newJoinsMap.containsKey(join.getKnowledgeId()))
                .map(FaqRobotKnowledgeJoinsPO::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(joinIdsToCheck)) {
            return Collections.emptyList();
        }

        // 2. 批量查询Milvus内容
        Map<String, String> milvusContentMap = new HashMap<>();
        List<VectorSearchResult> milvusResults = milvusService.selectByIds(
                MilvusPoolConfig.FAQ_COLLECTION_NAME, joinIdsToCheck);

        for (VectorSearchResult result : milvusResults) {
            milvusContentMap.put(result.getId(), result.getContent());
        }

        // 3. 比较内容变化
        List<String> questionChangedIds = new ArrayList<>();
        Map<String, FaqRobotKnowledgeJoinsPO> existingJoinMap = existingJoins.stream()
                .collect(Collectors.toMap(FaqRobotKnowledgeJoinsPO::getId, Function.identity()));

        for (String joinId : joinIdsToCheck) {
            FaqRobotKnowledgeJoinsPO join = existingJoinMap.get(joinId);
            if (join == null) {
                continue;
            }

            String knowledgeId = join.getKnowledgeId();
            String currentContent = knowledgeIdWithQuestionMap.get(knowledgeId);
            String milvusContent = milvusContentMap.get(joinId);

            // 内容变化检测：考虑null和空值情况
            if (currentContent != null && !currentContent.equals(milvusContent)) {
                log.info("检测到内容变化: joinId={}, 原内容: {}, 新内容: {}",
                        joinId, milvusContent, currentContent);
                questionChangedIds.add(joinId);
            }
        }

        // 4. 处理变更的向量
        if (!questionChangedIds.isEmpty()) {
            log.info("检测到 {} 个joins的内容变更，需要重新向量化", questionChangedIds.size());

            // 批量删除旧向量
            safeMilvusOperation(() ->
                    milvusService.deleteByIds(MilvusPoolConfig.FAQ_COLLECTION_NAME, questionChangedIds, null)
            );

            // 准备重新向量化的数据
            List<AutoEmbeddingDTO> changedDataList = new ArrayList<>();
            for (String joinId : questionChangedIds) {
                FaqRobotKnowledgeJoinsPO join = existingJoinMap.get(joinId);
                if (join == null) {
                    continue;
                }

                String knowledgeId = join.getKnowledgeId();
                String content = knowledgeIdWithQuestionMap.get(knowledgeId);
                if (content != null) {
                    changedDataList.add(AutoEmbeddingDTO.create(
                            joinId,
                            content,
                            join.getRobotId().toString()
                    ));
                }
            }

            // 批量插入新向量
            if (!changedDataList.isEmpty()) {
                safeMilvusOperation(() ->
                        milvusService.autoEmbeddingAndInsertMilvus(
                                MilvusPoolConfig.FAQ_COLLECTION_NAME,
                                changedDataList
                        )
                );
                log.info("完成 {} 个变更向量的重新向量化", changedDataList.size());
            }
        }

        return questionChangedIds;
    }

    /**
     * Milvus操作安全包装，防止异常影响主流程
     */
    private void safeMilvusOperation(Runnable operation) {
        try {
            operation.run();
        } catch (Exception e) {
            log.error("Milvus操作失败，将加入重试队列", e);
            throw e;
        }
    }

} 