package com.faw.work.ais.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.faw.work.ais.aic.config.MilvusPoolConfig;
import com.faw.work.ais.aic.model.dto.MilvusField;
import com.faw.work.ais.aic.service.EmbeddingService;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.entity.domain.ContentRulePO;
import com.faw.work.ais.entity.dto.*;
import com.faw.work.ais.entity.request.*;
import com.faw.work.ais.mapper.content.ContentRuleMapper;
import com.faw.work.ais.service.*;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class ContentReviewServiceImpl implements ContentReviewService {

    private final ContentMilvusService milvusService;
    private final EmbeddingService embeddingService;
    private final LLMVLService llmVlService;
    private final ContentLLMService contentLLMService;
//    private final RagDocumentSplitMapper ragDocumentSplitMapper;
//    private final RagDocumentSplitService ragDocumentSplitService;

    private final ContentRuleMapper contentRuleMapper;

    private final RedisTemplate<String, Object> redisTemplate;

    private static final String COMMENT_RULE = "Content:commentRule";
    private static final String POST_RULE = "Content:postRule";

    private final Integer BATCH_SIZE = 10;
    private final Integer LIMIT_PIXEL = 28*28*1000;
    private final Integer BATCH_LIMIT_PIXEL = 28*28*100;

    @Override
    public ContentSearchRes similarContent(String content, int topK) {
        if (content == null || content.isEmpty()) {
            throw new BizException("传入的内容为空");
        }
        ContentSearchRes contentSearchRes = new ContentSearchRes();
        try {
            // 1. 获取问题的向量表示
            float[] embedding = embeddingService.getEmbeddingV3(content);

            // 2. 在Milvus中搜索相似向量（默认返回最相似的1个结果）
            List<ContentSearchResult> searchResults = milvusService.searchByEmbedding(
                    MilvusPoolConfig.POST_COLLECTION_NAME,
                    embedding,
                    topK,
                    0.85f,
                    null
            );
            if (!searchResults.isEmpty()) {
                contentSearchRes.setContent(searchResults.get(0).getContent());
                contentSearchRes.setEmbedding(embedding);
                return contentSearchRes;
            } else {
                contentSearchRes.setEmbedding(embedding);
                return contentSearchRes;
            }
        } catch (Exception e) {
            log.error("搜索相似内容失败: {}", content, e);
            throw new BizException("搜索失败: " + e.getMessage());
        }
    }

    @Override
    public void storeEmbedding(String content, Integer totalScore, float[] embedding) {
        try {
            List<MilvusField> properties = new ArrayList<>();
            if (StrUtil.isNotBlank(content)) {
                properties.add(new MilvusField(MilvusPoolConfig.CONTENT, content));
            }
            properties.add(new MilvusField("biz_info", ""));

            milvusService.storeContentEmbedding(
                    MilvusPoolConfig.POST_COLLECTION_NAME,
                    null,
                    properties,
                    embedding);
        } catch (Exception e) {
            log.error("存储失败: {}", content, e);
            throw new BizException("存储失败: " + e.getMessage());
        }
    }

    @Override
    public List<ContentReviewResult> commentScore(CommentRequest request, float[] embedding) {

        try {
            // 评分开始
            String content = request.getContent();
            String topic = request.getTopic();
            String res = llmVlService.commentScore(topic, content);

            Integer totalScore = 0;
            List<ContentReviewResult> resultList = new ArrayList<>();
            JsonObject jsonObject = JsonParser.parseString(res).getAsJsonObject();
            for (String key : jsonObject.keySet()) {
                ContentReviewResult result = new ContentReviewResult();
                JsonObject innerObj = jsonObject.getAsJsonObject(key); // 获取子对象
                result.setType(key);
                if (innerObj.has("score")) { // 检查是否存在 score 字段
                    result.setScore(innerObj.get("score").getAsInt());
                    result.setReason(innerObj.get("reason").getAsString());
                    totalScore += innerObj.get("score").getAsInt(); // 累加分数
                }
                resultList.add(result);
            }
            resultList.add(new ContentReviewResult("totalScore", totalScore));
            jsonObject.add("totalScore", new JsonParser().parse(String.valueOf(totalScore)));

            List<MilvusField> properties = new ArrayList<>();
            if (StrUtil.isNotBlank(content)) {
                properties.add(new MilvusField(MilvusPoolConfig.CONTENT, content));
            }
            properties.add(new MilvusField("biz_info", ""));

            if (totalScore > 80) {
                milvusService.storeContentEmbedding(
                        MilvusPoolConfig.COMMENT_COLLECTION_NAME,
                        null,
                        properties,
                        embedding);
            }
            // 返回评分值
            return resultList;

        } catch (Exception e) {
            log.error("搜索相似内容失败: {}", request.getContent(), e);
            throw new BizException("搜索失败: " + e.getMessage());
        }

    }

    @Override
    public JSONObject commentReview(CommentRequest request, Integer times) {

        for (int attemp = 0; attemp < times;) {
            try {
                // 评分开始
                String content = request.getContent();
                String topic = request.getTopic();
                String res = llmVlService.commentScore(topic, content);

                Integer totalScore = 0;
                List<ContentReviewResult> resultList = new ArrayList<>();
                res = formatJson(res);
                JSONObject jsonObject = JSON.parseObject(res);
                for (String key : jsonObject.keySet()) {
                    ContentReviewResult result = new ContentReviewResult();
                    JSONObject innerObj = jsonObject.getJSONObject(key); // 获取子对象
                    result.setType(key);

                    if (innerObj.containsKey("score")) { // 检查是否存在 score 字段
                        result.setScore(innerObj.getIntValue("score"));
                        result.setReason(innerObj.getString("reason"));
                        totalScore += innerObj.getIntValue("score"); // 累加分数
                    }

                    resultList.add(result);
                }

                resultList.add(new ContentReviewResult("totalScore", totalScore));
                jsonObject.put("totalScore", totalScore); // Fastjson 会自动处理类型转换
                return jsonObject;
            } catch (Exception e) {
                log.error("评分失败: {}", request.getContent(), e);
                log.info("第{}次尝试评分", attemp+1);
                if (attemp < times -1){
                    attemp += 1;
                }else {
                    throw new BizException("评分失败: " + e.getMessage());
                }
            }
        }
        return new JSONObject();
    }

    @Override
    public JSONObject commentReview_v1(CommentRequest request, Integer times) {

        for (int attemp = 0; attemp < times;) {
            try {
                // 评分开始
                String content = request.getContent();
                String topic = request.getTopic();
                // 打印当前时间
                long startTime = System.currentTimeMillis();
                // 从redis查询评论规则,如果不存在则查数据库
                String commentRule = (String) redisTemplate.opsForValue().get(COMMENT_RULE);
                ContentRulePO contentRulePO = null;
                if (commentRule == null){
                    contentRulePO = contentRuleMapper.selectOne(
                            new QueryWrapper<ContentRulePO>()
                                    .eq("status", 1)
                                    .eq("type", 0)
                                    .orderByDesc("created_at")  // 根据你的时间字段修改
                                    .last("LIMIT 1")
                    );
                    if (contentRulePO != null){
                        commentRule = contentRulePO.getRule();
                        // 创建 JSON 解析器
                        commentRule = jsonParse(commentRule);
                        redisTemplate.opsForValue().set(COMMENT_RULE, commentRule);
                    } else {
                        Map<String, Object> map = new HashMap<>();
                        map.put("error", "没有已开启的规则");
                        return new JSONObject(map);
                    }
                }
                // // 查最新的一条
                // ContentCommentRulePO commentRule = commentRuleMapper.selectOne(
                //         new QueryWrapper<ContentCommentRulePO>()
                //                 .orderByDesc("created_at")  // 根据你的时间字段修改
                //                 .last("LIMIT 1")
                // );
                // 打印用时
                System.out.println("用时: " + (System.currentTimeMillis() - startTime) + "ms");
                String originalRule = commentRule;
                // 将originalRule转换为JSONObject再转换为List<ContentReviewResult>
                List<ContentReviewResult> orginalRules = convertToList(originalRule);
                String res = contentLLMService.commentScore(topic, content, orginalRules);

                Integer totalScore = 0;
                res = formatJson(res);
                JSONObject jsonObject = JSON.parseObject(res);
                for (String key : jsonObject.keySet()) {
                    JSONObject innerObj = jsonObject.getJSONObject(key); // 获取子对象
                    if (innerObj.containsKey("score")) { // 检查是否存在 score 字段
                        totalScore += innerObj.getIntValue("score"); // 累加分数
                    }
                }
                jsonObject.put("totalScore", totalScore); // Fastjson 会自动处理类型转换
                return jsonObject;
            } catch (Exception e) {
                log.error("评分失败: {}", request.getContent(), e);
                log.info("第{}次尝试评分", attemp+1);
                if (attemp < times -1){
                    attemp += 1;
                }else {
                    throw new BizException("评分失败: " + e.getMessage());
                }
            }
        }
        return new JSONObject();
    }

    public List<ContentReviewResult> convertToList(String originalRule){
        // 使用Feature.OrderedField保证JSON键值对的顺序
        JSONObject ruleJson = JSON.parseObject(originalRule, Feature.OrderedField);
        List<ContentReviewResult> ruleList = new ArrayList<>();
        for (String key : ruleJson.keySet()) {
            ContentReviewResult result = new ContentReviewResult();
            JSONObject innerObj = ruleJson.getJSONObject(key);
            result.setType(key);
            if (innerObj.containsKey("score")) {
                result.setScore(innerObj.getIntValue("score"));
                result.setReason(innerObj.getString("reason"));
            }
            ruleList.add(result);
        }
        return ruleList;
    }
    @Override
    public JSONObject postReview(PostRequest request, Integer times){
        String content = request.getContent();
        List<String> picUrls = request.getPicUrls();
        // 有优质动态可能，最高100分
        if (content.length() >= 100 && CollectionUtils.isNotEmpty(picUrls) && picUrls.size()>=4){
            // 先进行相似度搜索
            ContentSearchRes similarityRes = similarContent(content,1);
            if (similarityRes.getContent() == null){
                JSONObject jsonObject = postScore(request, times, true);
                Integer totalScore = jsonObject.getInteger("totalScore");
                if (totalScore>80){
                    storeEmbedding(content, totalScore, similarityRes.getEmbedding());
                }
                return jsonObject;
            }else {
                JSONObject obj = new JSONObject(1);
                obj.put("相似内容", similarityRes.getContent());
                return obj;
            }
        } else{
            // 无优质动态可能，最高80分
            return postScore(request, times, false);
        }

    }

    @Override
    public JSONObject postReview_v1(PostRequest request, Integer times){
        String content = request.getContent();
        List<String> picUrls = request.getPicUrls();
        // 有优质动态可能，最高100分
        if (content.length() >= 100 && CollectionUtils.isNotEmpty(picUrls) && picUrls.size()>=4){
            // 先进行相似度搜索
            ContentSearchRes similarityRes = similarContent(content,1);
            if (similarityRes.getContent() == null){
                JSONObject jsonObject = postScore_v1(request, times, true);
                Integer totalScore = jsonObject.getInteger("totalScore");
                if (totalScore>80){
                    storeEmbedding(content, totalScore, similarityRes.getEmbedding());
                }
                return jsonObject;
            }else {
                JSONObject obj = new JSONObject(1);
                obj.put("相似内容", similarityRes.getContent());
                obj.put("totalScore", 0);
                return obj;
            }
        } else{
            // 无优质动态可能，最高80分
            return postScore_v1(request, times, false);
        }

    }

    @Override
    public JSONObject postScore(PostRequest request, Integer times, Boolean flag) {
        for (int attemp = 0; attemp < times;) {
            try {
                // 评分开始
                String topic = request.getTopic();
                String content = request.getContent();
                List<String> picUrls = request.getPicUrls();

                content = content.replaceAll("\n", "");
                if (CollectionUtils.isNotEmpty(picUrls)){
                    picUrls = urlProcessor(picUrls, LIMIT_PIXEL);
                }
                String res = llmVlService.postScore(topic, content, picUrls, flag);

                Integer totalScore = 0;
                List<ContentReviewResult> resultList = new ArrayList<>();
                res = formatJson(res);
                JSONObject jsonObject = JSON.parseObject(res);
                for (String key : jsonObject.keySet()) {
                    ContentReviewResult result = new ContentReviewResult();
                    JSONObject innerObj = jsonObject.getJSONObject(key); // 获取子对象
                    result.setType(key);

                    if (innerObj.containsKey("score")) { // 检查是否存在 score 字段
                        result.setScore(innerObj.getIntValue("score"));
                        result.setReason(innerObj.getString("reason"));
                        totalScore += innerObj.getIntValue("score"); // 累加分数
                    }

                    resultList.add(result);
                }

                resultList.add(new ContentReviewResult("totalScore", totalScore));
                jsonObject.put("totalScore", totalScore); // Fastjson 会自动处理类型转换
                return jsonObject;
            } catch (Exception e) {
                log.error("评分失败: {}", request.getContent(), e);
                log.info("第{}次尝试评分", attemp+1);
                if (attemp < times -1){
                    attemp += 1;
                }else {
                    throw new BizException("评分失败: " + e.getMessage());
                }
            }
        }
        return new JSONObject();
    }

    public JSONObject postScore_v1(PostRequest request, Integer times, Boolean flag) {
        for (int attemp = 0; attemp < times;) {
            try {
                // 评分开始
                String topic = request.getTopic();
                String content = request.getContent();
                List<String> picUrls = request.getPicUrls();

                content = content.replaceAll("\n", "");
//                if (CollectionUtils.isNotEmpty(picUrls)){
//                    picUrls = urlProcessor(picUrls, LIMIT_PIXEL);
//                }
                // 打印当前时间
                long startTime = System.currentTimeMillis();
                // 从redis查询评论规则,如果不存在则查数据库
                String postRule = (String) redisTemplate.opsForValue().get(POST_RULE);
                ContentRulePO postRulePO = null;
                if (postRule == null){
                    postRulePO = contentRuleMapper.selectOne(
                            new QueryWrapper<ContentRulePO>()
                                    .eq("status", 1)
                                    .eq("type", 1)
                                    .orderByDesc("created_at")  // 根据你的时间字段修改
                                    .last("LIMIT 1")
                    );
                    if (postRulePO != null){
                        postRule = postRulePO.getRule();
                        postRule = jsonParse(postRule);
                        redisTemplate.opsForValue().set(POST_RULE, postRule);
                    } else {
                        Map<String, Object> map = new HashMap<>();
                        map.put("error", "没有已开启的规则");
                        return new JSONObject(map);
                    }
                }
                // 打印用时
                System.out.println("用时: " + (System.currentTimeMillis() - startTime) + "ms");
                String originalRule = postRule;
                // 将originalRule转换为JSONObject再转换为List<ContentReviewResult>
                List<ContentReviewResult> orginalRules = convertToList(originalRule);

                String res = contentLLMService.postScore(topic, content, picUrls, flag, orginalRules);

                Integer totalScore = 0;
                List<ContentReviewResult> resultList = new ArrayList<>();
                res = formatJson(res);
                JSONObject jsonObject = JSON.parseObject(res);
                for (String key : jsonObject.keySet()) {
                    ContentReviewResult result = new ContentReviewResult();
                    JSONObject innerObj = jsonObject.getJSONObject(key); // 获取子对象
                    result.setType(key);

                    if (innerObj.containsKey("score")) { // 检查是否存在 score 字段
                        result.setScore(innerObj.getIntValue("score"));
                        result.setReason(innerObj.getString("reason"));
                        totalScore += innerObj.getIntValue("score"); // 累加分数
                    }

                    resultList.add(result);
                }

                resultList.add(new ContentReviewResult("totalScore", totalScore));
                jsonObject.put("totalScore", totalScore); // Fastjson 会自动处理类型转换
                return jsonObject;
            } catch (Exception e) {
                log.error("评分失败: {}", request.getContent(), e);
                log.info("第{}次尝试评分", attemp+1);
                if (attemp < times -1){
                    attemp += 1;
                }else {
                    throw new BizException("评分失败: " + e.getMessage());
                }
            }
        }
        return new JSONObject();
    }

    @Async("contentSummaryExecutor")
    @Override
    public void commentSummary(List<CommentSummaryRequest> requests) {
        try {
            // 打印当前时间
            long startTime = System.currentTimeMillis();
            // 过去的规则，从库中取
            // 查当前启用的规则
            ContentRulePO commentRule = contentRuleMapper.selectOne(
                    new QueryWrapper<ContentRulePO>()
                            .eq("status", 1)
                            .eq("type", 0)
                            .orderByDesc("created_at")  // 根据你的时间字段修改
                            .last("LIMIT 1")
            );
            if (commentRule == null){
                log.error("评论总结失败:没有启用中的评论规则");
                throw new BizException("评论总结失败: 没有启用中的评论规则");
            }
            String rule = commentRule.getRule();
            // 总结开始
            String res = contentLLMService.commentSummary(requests, rule);

            List<ContentReviewResult> resultList = new ArrayList<>();
            res = formatJson(res);
            // 入库
            ContentRulePO commentRulePO = new ContentRulePO();
            commentRulePO.setRule(res);
            commentRulePO.setStatus(0);
            commentRulePO.setType(0);
            contentRuleMapper.insert(commentRulePO);

            Integer totalScore = 0;
            JSONObject jsonObject = JSON.parseObject(res);
            for (String key : jsonObject.keySet()) {
                ContentReviewResult result = new ContentReviewResult();
                JSONObject innerObj = jsonObject.getJSONObject(key); // 获取子对象
                result.setType(key);

                if (innerObj.containsKey("score")) { // 检查是否存在 score 字段
                    result.setScore(innerObj.getIntValue("score"));
                    result.setReason(innerObj.getString("reason"));
                    totalScore += innerObj.getIntValue("score"); // 累加分数
                }

                resultList.add(result);
            }

            resultList.add(new ContentReviewResult("totalScore", totalScore));
            jsonObject.put("totalScore", totalScore); // Fastjson 会自动处理类型转换
            // 打印用时
            log.info("用时: " + (System.currentTimeMillis() - startTime) + "ms");
            // todo 回调接口
        } catch (Exception e) {
            log.error("评论总结失败", e);
            throw new BizException("评论总结失败: " + e.getMessage());
        }
    }

    @Async("contentSummaryExecutor")
    @Override
    public void postSummary(List<PostSummaryRequest> requests) {
        try {
            // 打印当前时间
            long startTime = System.currentTimeMillis();
            // 查最新的一条
            ContentRulePO postRule = contentRuleMapper.selectOne(
                    new QueryWrapper<ContentRulePO>()
                            .eq("status", 1)
                            .eq("type", 1)
                            .orderByDesc("created_at")  // 根据你的时间字段修改
                            .last("LIMIT 1")
            );
            if (postRule == null){
                log.error("动态总结失败:没有启用中的动态规则");
                throw new BizException("动态总结失败: 没有启用中的动态规则");
            }
            String rule = postRule.getRule();
            // 处理图片链接
            for (PostSummaryRequest req : requests) {
                if (req.getPicUrls() != null && !req.getPicUrls().isEmpty()) {
                    List<String> processedUrls = urlProcessor(req.getPicUrls(), BATCH_LIMIT_PIXEL);
                    req.setPicUrls(processedUrls);
                }
            }
            // 批量总结开始
            String summaryAll = contentLLMService.postSummary(requests, rule);
            summaryAll = formatJson(summaryAll);
            // 入库
            ContentRulePO postRulePO = new ContentRulePO();
            postRulePO.setRule(summaryAll);
            postRulePO.setStatus(0);
            postRulePO.setType(1);
            contentRuleMapper.insert(postRulePO);

            // 打印用时
            log.info("用时: " + (System.currentTimeMillis() - startTime) + "ms");
            // todo 回调接口
        } catch (Exception e) {
            log.error("总结失败", e);
            throw new BizException("总结失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject postReviewTruth(PostRequest request, int topK) {
        // 先执行参数验证
//        List<RagDocumentSplitPO> results = validateInput(request.getContent(), topK);
//
//        if (!results.isEmpty()){
//            String query = results.stream().map(RagDocumentSplitPO::getContent).collect(Collectors.joining("\n"));
//            String ss = llmVlService.truthValidate(request.getContent(), query);
//            if ("是".equals(ss)){
//                return postReview(request, 1);
//            }else {
//                Map<String, Object> map = new HashMap<>();
//                map.put("fake", "车辆参数与事实不符");
//                return new JSONObject(map);
//            }
//        } else{
            return postReview(request, 1);// 非车型参数或未获取到直接走AI
//        }

    }

    @Override
    public String editRule(ContentRulePO request) {
        // 更新规则
        contentRuleMapper.updateById(request);

        // 根据类型选择缓存key
        String cacheKey = request.getType() == 0 ? COMMENT_RULE : POST_RULE;

        if (request.getStatus() == 1) {
            // 启用当前规则，禁用其他规则
            contentRuleMapper.disableOtherRules(request.getId(), request.getType());
            // 查询最新规则并写入缓存
            ContentRulePO enabledRule = contentRuleMapper.selectById(request.getId());
            if (enabledRule != null && enabledRule.getRule() != null) {
                String newRule = jsonParse(enabledRule.getRule());
                redisTemplate.opsForValue().set(cacheKey, newRule);
            }
        } else {
            // 禁用则删除缓存
            redisTemplate.delete(cacheKey);
        }
        return "";
    }

    @Override
    public String deleteRule(Long id) {
        // 查询启用状态，如果已启用则返回启用状态无法删除
        // 1. 查询规则
        ContentRulePO rule = contentRuleMapper.selectById(id);
        if (rule == null) {
            throw new BizException("规则不存在");
        }
        // 2. 判断是否启用
        if (rule.getStatus() != null && rule.getStatus() == 1) {
            throw new BizException("启用状态下的规则无法删除");
        }
        // 3. 删除规则
        contentRuleMapper.deleteById(id);
        return "删除成功";
    }

//    public List<RagDocumentSplitPO> validateInput(String userInput, Integer topK) {
//        // 提取车系名称和参数
//        String params = llmVlService.extractVehicleParam(userInput);
//        params = formatJson(params);
//        JSONObject jsonObject = JSON.parseObject(params);
//        // 判断是否只有一个name字段
//        if (jsonObject.keySet().size() == 1) {
//            log.info(userInput + ",非车系参数描述");
//            return null;
//        }
//        String bizInfo = jsonObject.getString("name");
//        if (StringUtils.isEmpty(bizInfo)) {
//            log.info(userInput + ",未提及具体车辆名称");
//            return null;
//        }
////        try {
////            // 1. 获取查询内容的向量表示
////            float[] queryEmbedding = embeddingService.getEmbedding(userInput);
////
////            String filterString = "biz_info == '" + bizInfo + "'";
////            // 2. 在Milvus中搜索相似向量
////            List<ContentSearchResult> searchResults = milvusService.searchByEmbedding(
////                    MilvusPoolConfig.FAQ_COLLECTION_NAME,
////                    queryEmbedding,
////                    topK,
////                    0.85f,
////                    filterString);
////            if (!searchResults.isEmpty()) {
////                return searchResults;
////            } else {
////                log.info(userInput + ",未获取到相关车型参数");
////                return null;
////            }
////        } catch (Exception e) {
////            log.error("搜索相似内容失败: ", e);
////            throw new BizException("搜索失败: " + e.getMessage());
////        }
//    public List<RagDocumentSplitPO> validateInput(String userInput, Integer topK) {
//        // 提取车系名称和参数
//        String params = llmVlService.extractVehicleParam(userInput);
//        params = formatJson(params);
//        JSONObject jsonObject = JSON.parseObject(params);
//        // 判断是否只有一个name字段
//        if (jsonObject.keySet().size() == 1) {
//            log.info(userInput + ",非车系参数描述");
//            return null;
//        }
//        String bizInfo = jsonObject.getString("name");
//        if (StringUtils.isEmpty(bizInfo)) {
//            log.info(userInput + ",未提及具体车辆名称");
//            return null;
//        }
//        try {
//            // 1. 获取查询内容的向量表示
//            float[] queryEmbedding = embeddingService.getEmbedding(userInput);
//
//            String filterString = "biz_info == '" + bizInfo + "'";
//            // 2. 在Milvus中搜索相似向量
//            List<ContentSearchResult> searchResults = milvusService.searchByEmbedding(
//                    MilvusPoolConfig.FAQ_COLLECTION_NAME,
//                    queryEmbedding,
//                    topK,
//                    0.85f,
//                    filterString);
//
//            if (searchResults.isEmpty()) {
//                log.info("未找到相似内容");
//                return new ArrayList<>();
//            }
//
//            // 3. 从数据库获取对应的文档片段
//            List<RagDocumentSplitPO> results = searchResults.stream()
//                    .map(result -> {
//                        // 如果后续关联到应用id，这里需要增加documentId（appId关联）的查询条件
//                        RagDocumentSplitPO split = ragDocumentSplitService.getById(result.getId().toString());
//                        if (split != null) {
//                            // 更新命中次数
//                            split.setScore(result.getScore());
//                            split.setHitCount(split.getHitCount() + 1);
//                            // 将相似度记录到日志
//                            log.info("文档片段ID: {}, 相似度分数: {}", split.getId(), result.getScore());
//                            ragDocumentSplitMapper.updateHitCount(split.getId(), split.getScore());
//                        }
//                        return split;
//                    })
//                    .filter(Objects::nonNull)
//                    .collect(Collectors.toList());
//
//            log.info("搜索完成，找到{}个相似文档片段", results.size());
//            return results;
//
//        } catch (Exception e) {
//            log.error("搜索相似内容失败: query={}", userInput, e);
//            return new ArrayList<>();
//        }
//    }


    private String formatJson(String res) {
        return res.replaceAll("json", "")  // 删除开头标记
                .replaceAll("```", "")         // 删除结尾标记
                .trim();                       // 清理首尾空格
    }
    private List<String> urlProcessor(List<String> urls, int limit){
        String end = "?imageMogr2/thumbnail/";
        List<String> fileUrls = new ArrayList<>();
        for (String originalUrl : urls) {
//            如果不以http开头
            if (!originalUrl.startsWith("http")){
                continue;
            }
            // 执行字符串替换操作
            String processedUrl = originalUrl.replace(" ", "")
                    .replace("?&", "?")
                    .replace("!50p", limit + "@");

            // 添加后缀条件判断
            if (!originalUrl.contains("thumbnail")) {
                processedUrl += end + limit + "@";
            }

            fileUrls.add(processedUrl);
        }
        return fileUrls;
    }

    private String jsonParse(String original){
        ObjectMapper mapper = new ObjectMapper();

        try {
            // 1. 将字符串解析为 JSON 对象
            Object jsonObject = mapper.readValue(original, Object.class);

            // 2. 将对象重新序列化为紧凑格式（无换行和缩进）
            String compactJson = mapper.writeValueAsString(jsonObject);

            return compactJson;
        } catch (Exception e) {
            // 异常处理
            throw new BizException("评分失败: JSON转换失败");
        }
    }

    public boolean isCarParamContext(String userInput) {
        // 是否出现"车长"、"车宽"、"车高"、"轴距"、"轮毂"、"百公里加速"、"油耗"、"零百"、"最高车速"等
        String[] keywords = {"车长", "车宽", "车高", "轴距", "轮毂", "百公里加速", "油耗", "零百", "最高车速"};
        for (String keyword : keywords) {
            if (userInput.contains(keyword)) {
                return true;
            }
        }
        return false;
    }
}
