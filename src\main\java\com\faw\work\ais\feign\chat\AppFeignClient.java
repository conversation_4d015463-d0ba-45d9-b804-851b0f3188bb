package com.faw.work.ais.feign.chat;

import com.faw.work.ais.common.dto.chat.AppRequest;
import com.faw.work.ais.common.dto.chat.AppResponse;
import com.faw.work.ais.common.dto.chat.MaintenanceEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 智联APPFeign调用接口
 *
 * <AUTHOR>
 * @since 2025-06-25 10:21
 */
@FeignClient(value = "AppFeignClient", url = "${app.host:}")
public interface AppFeignClient {

    /**
     * 一键预约维保接口
     *
     * @param request 请求体
     * @return 解析结果
     */
    @PostMapping("/lxxgateway/repair/getAppointmentRepairInfo")
    AppResponse<MaintenanceEntity> orderMaintenance(@RequestBody AppRequest request);

}
