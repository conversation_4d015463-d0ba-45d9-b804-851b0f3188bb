package com.faw.work.ais.aic.common.enums;


import lombok.Getter;

/**
 * 文本嵌入模型类型枚举
 *
 * <AUTHOR>
 * @date 2025/05/17
 */
@Getter
public enum EmbeddingModelTypeEnum {

    /**
     * 文本嵌入模型V1版本
     */
    TEXT_EMBEDDING_V1("text-embedding-v1", "文本嵌入模型V1版本"),

    /**
     * 文本嵌入模型V2版本
     */
    TEXT_EMBEDDING_V2("text-embedding-v2", "文本嵌入模型V2版本"),

    /**
     * 文本嵌入模型V3版本
     */
    TEXT_EMBEDDING_V3("text-embedding-v3", "文本嵌入模型V3版本");

    EmbeddingModelTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private final String code;

    private final String msg;
}
