package com.faw.work.ais.model;

import java.io.Serializable;

/**
 * system_call_back_url
 * <AUTHOR>
public class SystemCallBackUrl implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 系统id
     */
    private String systemId;

    /**
     * 回调地址完整服务路径；
     */
    private String callBackUrl;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 停用标识；1-停用；0-未停用；
     */
    private String stop;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSystemId() {
        return systemId;
    }

    public void setSystemId(String systemId) {
        this.systemId = systemId;
    }

    public String getCallBackUrl() {
        return callBackUrl;
    }

    public void setCallBackUrl(String callBackUrl) {
        this.callBackUrl = callBackUrl;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getStop() {
        return stop;
    }

    public void setStop(String stop) {
        this.stop = stop;
    }
}