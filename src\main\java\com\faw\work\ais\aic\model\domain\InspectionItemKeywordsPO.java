package com.faw.work.ais.aic.model.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 质检关键词表
 * <AUTHOR>
 */
@Data
@TableName("inspection_item_keywords")
public class InspectionItemKeywordsPO implements Serializable {

    @TableId("id")
    private Long id;

    @TableField("keywords_type")
    private String keywordsType;

    @TableField("keywords_rule")
    private String keywordsRule;

    @TableField("inspection_rule_id")
    private Long inspectionRuleId;

    @TableField("keywords_condition")
    private String keywordsCondition;

    @TableField("status")
    private Integer status;

    @TableField("created_by")
    private String createdBy;

    @TableField("created_at")
    private Timestamp createdAt;

    @TableField("updated_by")
    private String updatedBy;

    @TableField("updated_at")
    private Timestamp updatedAt;
}

