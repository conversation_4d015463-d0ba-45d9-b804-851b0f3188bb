package com.faw.work.ais.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
* 数量统计表
* Created  by Mr.hp
* DateTime on 2025-01-13 14:12:35
* <AUTHOR> Mr.hp
* @ApiModel(value = "QuantityStatistics", description = "数量统计表")
*/
@Data
@NoArgsConstructor
@Schema(description = "QuantityStatisticsDayValue", name = "数量统计dayValue")
public class QuantityStatisticsDayValue implements Serializable {

    private static final long serialVersionUID = 1L;



    /**
    * 时间点（yyyy-MM-dd）
    * @ApiModelProperty(value = "时间点（yyyy-MM-dd）")
    */
    @Schema(description = "时间点（yyyy-MM-dd）")
    private String timing;

    /**
    * 系统ID
    * @ApiModelProperty(value = "系统ID")
    */
    @Schema(description = "系统ID")
    private String systemId;

    /**
     * 系统ID
     * @ApiModelProperty(value = "系统ID")
     */
    @Schema(description = "系统ID")
    private String systemName;

    /**
     * 系统ID
     * AI审核任务通过率趋势
     */
    @Schema(description = "AI审核任务通过率")
    private BigDecimal aiAuditTaskPassRate;

    /**
     * 系统ID
     * 人工复审材料准确率
     */
    @Schema(description = "人工复审材料准确率")
    private BigDecimal manualAccuracyRate;


    /**
     * 系统ID
     * 较昨日
     */
    @Schema(description = "ai较昨日")
    private BigDecimal aiCompareYesterday;
    /**
     * 系统ID
     * 较昨日
     */
    @Schema(description = "人工较昨日")
    private BigDecimal manualCompareYesterday;

}
