package com.faw.work.ais.model.base;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description:
 * @author: Admin
 * @date: 2022年08月31日 12:45
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageRequest implements Serializable {

    @Schema(name= "版本控制")
    private static final long serialVersionUID = -8180084402220580047L;

    @Schema(name= "页大小")
    private Integer pageSize = 10;

    @Schema(name= "当前页数")
    private Integer pageNum = 1;

}
