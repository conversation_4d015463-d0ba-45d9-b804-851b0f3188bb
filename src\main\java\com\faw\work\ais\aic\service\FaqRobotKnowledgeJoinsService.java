package com.faw.work.ais.aic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.aic.model.domain.FaqRobotKnowledgeJoinsPO;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * FAQ机器人知识关联Service接口
 * <AUTHOR>
 */
public interface FaqRobotKnowledgeJoinsService extends IService<FaqRobotKnowledgeJoinsPO> {
    /**
     * 按机器人id选择全部
     *
     * @param robotId 机器人id
     * @return {@link List }<{@link FaqRobotKnowledgeJoinsPO }>
     */
    List<FaqRobotKnowledgeJoinsPO> selectAllByRobotId(String robotId);


    /**
     * 数据从测试环境转移到正式环境
     *
     * @param robotId 机器人id
     */
    @Async("faqPublishExecutor")
    void migrateAllData(List<String> robotId) throws InterruptedException;

    /**
     * （发布知识）迁移知识到环境
     *
     * @param knowledgeId 知识id
     * @throws InterruptedException 中断异常
     */
    void migrateKnowledge(String knowledgeId) throws InterruptedException;

    /**
     * 检查类目id
     *
     * @param categoryId 类目id
     */
    void checkCategoryId(List<String> categoryId);
}