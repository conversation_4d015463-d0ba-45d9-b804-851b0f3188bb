package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cloud.ai.dashscope.rerank.DashScopeRerankOptions;
import com.alibaba.cloud.ai.model.RerankRequest;
import com.alibaba.cloud.ai.model.RerankResponse;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcp.common.files.service.FileStorageService;
import com.dcp.common.files.vo.FileInfoVO;
import com.dcp.common.rest.Result;
import com.faw.work.ais.aic.common.enums.EmbeddingModelTypeEnum;
import com.faw.work.ais.aic.common.enums.TrainingTypeEnum;
import com.faw.work.ais.aic.common.util.BaiLianUtils;
import com.faw.work.ais.aic.common.util.RedisService;
import com.faw.work.ais.aic.common.util.StrUtils;
import com.faw.work.ais.aic.config.BaiLianAppConfig;
import com.faw.work.ais.aic.config.MilvusPoolConfig;
import com.faw.work.ais.aic.feign.DmsKnowledgeFeignClient;
import com.faw.work.ais.aic.feign.dto.ApiResponse;
import com.faw.work.ais.aic.feign.dto.KnowledgeFileInfoResponse;
import com.faw.work.ais.aic.feign.dto.KnowledgeNewInfoRequest;
import com.faw.work.ais.aic.mapper.rag.RagDocumentMapper;
import com.faw.work.ais.aic.model.domain.MilvusRow;
import com.faw.work.ais.aic.model.domain.RagDocumentPO;
import com.faw.work.ais.aic.model.domain.RagDocumentSplitPO;
import com.faw.work.ais.aic.model.domain.RagKnowledgePO;
import com.faw.work.ais.aic.model.dto.EmbeddingPropertyDTO;
import com.faw.work.ais.aic.model.dto.MilvusField;
import com.faw.work.ais.aic.model.dto.MilvusSearchResult;
import com.faw.work.ais.aic.model.request.*;
import com.faw.work.ais.aic.model.response.RagDocumentProcessAllResponse;
import com.faw.work.ais.aic.model.response.RagKnowledgeDocumentBindResponse;
import com.faw.work.ais.aic.model.response.SimilarContentSearchResponse;
import com.faw.work.ais.aic.service.*;
import com.faw.work.ais.common.enums.Constants;
import com.faw.work.ais.common.enums.RagDocumentFileTypeEnum;
import com.faw.work.ais.common.enums.RagDocumentParseStatusEnum;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.common.util.UserThreadLocalUtil;
import com.google.common.collect.Lists;
import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.DocumentParser;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.document.loader.UrlDocumentLoader;
import dev.langchain4j.data.document.parser.apache.pdfbox.ApachePdfBoxDocumentParser;
import dev.langchain4j.data.document.parser.apache.tika.ApacheTikaDocumentParser;
import dev.langchain4j.data.document.splitter.DocumentByParagraphSplitter;
import dev.langchain4j.data.segment.TextSegment;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.faw.work.ais.aic.config.MilvusPoolConfig.RAG_TENANT_CLIENT_KEY;

/**
 * 文档表 服务实现类
 *
 * <AUTHOR> Assistant
 */
@Service
@Slf4j
public class RagDocumentServiceImpl extends ServiceImpl<RagDocumentMapper, RagDocumentPO> implements RagDocumentService {
    public static final String ONE_STRING = "1";
    @Value("${rag.document.uploadSize:50}")
    private Integer maxUploadSize;

    private static final String FILE_URL_MAP_KEY = "aic:flag-baby:file-url:map";
    private static final long TTL = 12 * 60 * 60;

    @Autowired
    private RedisService redisService;

    @Autowired
    private BaiLianAppConfig baiLianAppConfig;

    @Autowired
    private RagDocumentSplitService ragDocumentSplitService;

    @Autowired
    private MilvusService milvusService;

    @Autowired
    private EmbeddingService embeddingService;

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private RagDocumentMapper ragDocumentMapper;

    @Autowired
    private RagDocumentService self;
    @Autowired
    private RagKnowledgeService ragKnowledgeService;
    @Autowired
    private RagKnowledgeDocumentJoinsService ragKnowledgeDocumentJoinsService;

    @Autowired
    private DmsKnowledgeFeignClient dmsKnowledgeFeignClient;


    @Override
    public List<RagDocumentPO> getDocumentList(RagDocumentPO request) {
        LambdaQueryWrapper<RagDocumentPO> queryWrapper = new LambdaQueryWrapper<>();

        if (request != null) {
            // 按条件查询
            if (request.getCategoryId() != null) {
                queryWrapper.eq(RagDocumentPO::getCategoryId, request.getCategoryId());
            }

            if (request.getFileType() != null && !request.getFileType().isEmpty()) {
                queryWrapper.eq(RagDocumentPO::getFileType, request.getFileType());
            }

            if (request.getParseStatus() != null && !request.getParseStatus().isEmpty()) {
                queryWrapper.eq(RagDocumentPO::getParseStatus, request.getParseStatus());
            }

        }

        // 按ID降序排序
        queryWrapper.orderByDesc(RagDocumentPO::getId);

        return this.list(queryWrapper);
    }

    @Override
    public Page<RagDocumentPO> getDocumentPage(RagDocumentPageRequest request) {
        Page<RagDocumentPO> page = new Page<>(request.getPageNum(), request.getPageSize());
        LambdaQueryWrapper<RagDocumentPO> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (request.getCategoryId() != null) {
            queryWrapper.eq(RagDocumentPO::getCategoryId, request.getCategoryId());
        }

        if (request.getFileType() != null && !request.getFileType().isEmpty()) {
            queryWrapper.eq(RagDocumentPO::getFileType, request.getFileType());
        }

        if (request.getParseStatus() != null && !request.getParseStatus().isEmpty()) {
            queryWrapper.eq(RagDocumentPO::getParseStatus, request.getParseStatus());
        }


        // 按ID降序排序
        queryWrapper.orderByDesc(RagDocumentPO::getId);

        return this.page(page, queryWrapper);
    }

    @Override
    public Page<RagDocumentPO> pageWithCustom(Page<RagDocumentPO> page, Wrapper<RagDocumentPO> queryWrapper) {
        return this.page(page, queryWrapper);
    }

    @Override
    public void splitDocument(Long documentId) {
        RagDocumentPO document = this.getById(documentId);
        if (document == null) {
            throw new BizException("文档不存在: documentId=" + documentId);
        }
        if (RagDocumentParseStatusEnum.PARSED.getCode().equals(document.getParseStatus())) {
            throw new BizException("文档已经解析完成，请删除索引后,再删除切片信息，重新解析: documentId=" + documentId);
        }
        if (RagDocumentParseStatusEnum.PARSING.getCode().equals(document.getParseStatus())) {
            throw new BizException("文档正在解析中: documentId=" + documentId);
        }

        String fileUrl = document.getFileUrl();

        if (StrUtil.isBlank(fileUrl)) {
            throw new BizException("文档文件URL为空: documentId=" + documentId);
        }

        log.info("开始解析文档: documentId={}", documentId);

        //  更新文档解析状态为进行中
        document.setParseStatus(RagDocumentParseStatusEnum.PARSING.getCode());
        self.updateByIdTran(document);

        // 2. 根据文件类型选择合适的文档解析器
        DocumentParser documentParser = this.getDocumentParser(document.getFileType());
        // 3. 加载并解析文档
        Document loadedDoc = UrlDocumentLoader.load(fileUrl, documentParser);

        // Document loadedDoc = FileSystemDocumentLoader.loadDocument("D:\\Git\\CSP\\AI\\sa-0214_msa_aio_be\\src\\main\\resources\\测试.pdf", documentParser);


        if (loadedDoc == null || loadedDoc.text() == null || loadedDoc.text().trim().isEmpty()) {
            throw new BizException("文档内容为空");
        }

        log.info("文档读取完成，文字数: {}", loadedDoc.text().length());

        // 4. 准备分割文档

        // 切分文档
        DocumentSplitter splitter = this.getDocumentSplitter(documentId);

        List<TextSegment> segments = splitter.split(loadedDoc);

        if (segments.isEmpty()) {
            throw new BizException("文档切分失败,内容为空");
        }


        // 6. 保存文档片段
        this.createAndSaveDocumentSplits(documentId, segments);


        // 7. 更新文档状态
        document.setParseStatus(RagDocumentParseStatusEnum.PARSED.getCode());
        this.updateById(document);

        log.info("文档解析完成: documentId={}", documentId);
    }

    /**
     * 获取文档拆分器
     *
     * @return {@link DocumentSplitter }
     */
    private DocumentSplitter getDocumentSplitter(Long documentId) {
        // 根据文档ID获取分片配置
        RagDocumentPO document = this.getById(documentId);
        Integer chunkLength = document.getChunkLength();
        Integer overlapLength = document.getOverlapLength();

        log.info("文档分片配置: chunkLength={}, overlapLength={}", chunkLength, overlapLength);
        return new DocumentByParagraphSplitter(chunkLength, overlapLength);
    }

    @Override
    public boolean vectorDocumentSplits(Long documentId) {

        RagDocumentPO document = ragDocumentMapper.getDetailById(documentId);
        String collectionName = document.getCollectionName();
        if (StrUtil.isBlank(collectionName)) {
            throw new BizException("文档未绑定到知识库");
        }

        // 检查文档是否已成功解析
        if (!StringUtils.equalsAny(document.getParseStatus(),
                RagDocumentParseStatusEnum.PARSED.getCode(),
                RagDocumentParseStatusEnum.VECTOR_FAILED.getCode())) {
            throw new BizException("文档未成功解析或无法向量化");
        }

        // 检查文档是否已成功解析
        if (RagDocumentParseStatusEnum.VECTOR_SUCCESS.getCode().equals(document.getParseStatus())) {
            throw new BizException("文档已经向量化完成，请删除索引后重新向量化");
        }


        log.info("开始向量化文档片段: documentId={}", documentId);

        try {
            // 1. 获取该文档的所有片段
            RagDocumentSplitPO queryParam = new RagDocumentSplitPO();
            queryParam.setDocumentId(documentId);
            List<RagDocumentSplitPO> documentSplits = ragDocumentSplitService.getList(queryParam);

            if (documentSplits.isEmpty()) {
                throw new BizException("文档片段为空");
            }

            log.info("找到{}个文档片段待向量化", documentSplits.size());

            List<String> textList = documentSplits.stream().map(RagDocumentSplitPO::getContent).toList();
            List<EmbeddingPropertyDTO> embeddingList = embeddingService.getEmbeddingList(EmbeddingModelTypeEnum.TEXT_EMBEDDING_V3, textList);

            // 检查embeddingList和documentSplits的数量是否一致
            if (embeddingList.size() != documentSplits.size()) {
                throw new BizException("向量化结果数量与文档片段数量不匹配");
            }

            List<MilvusRow> rows = new ArrayList<>();
            for (int i = 0; i < embeddingList.size(); i++) {
                EmbeddingPropertyDTO embeddingPropertyDTO = embeddingList.get(i);
                RagDocumentSplitPO documentSplit = documentSplits.get(i);

                MilvusRow row = new MilvusRow();
                // 使用文档片段的ID作为vectorId
                row.setVectorId(String.valueOf(documentSplit.getId()));
                row.setEmbedding(embeddingPropertyDTO.getEmbedding());

                List<MilvusField> properties = Lists.newArrayList();
                properties.add(new MilvusField(MilvusPoolConfig.DOCUMENT_ID_FIELD, String.valueOf(documentId)));
                properties.add(new MilvusField(MilvusPoolConfig.LABEL, document.getLabel()));
                if (documentSplit.getKeywords() != null) {
                    properties.add(new MilvusField("keywords", documentSplit.getKeywords()));
                }

                row.setProperties(properties);
                rows.add(row);
            }


            milvusService.saveEmbeddingBatch(RAG_TENANT_CLIENT_KEY, collectionName, rows);
            rows.clear();

            // 3. 更新文档向量化状态
            document.setParseStatus(RagDocumentParseStatusEnum.VECTOR_SUCCESS.getCode());
            return this.updateById(document);

        } catch (Exception e) {
            log.error("向量化文档片段失败: documentId={}", documentId, e);
            document.setParseStatus(RagDocumentParseStatusEnum.VECTOR_FAILED.getCode());
            document.setParseError(e.getMessage());
            this.updateById(document);
            return false;
        }
    }


    @Override
    public List<SimilarContentSearchResponse> searchSimilarContentNew(SearchContentRequest request) {


        Long ragKnowledgeId = request.getRagKnowledgeId();
        RagKnowledgePO ragKnowledge = ragKnowledgeService.getById(ragKnowledgeId);
        float similarityThreshold = ragKnowledge.getSimilarityThreshold();
        int topK = ragKnowledge.getTopK();
        //        是否重写问题
        String reQueryOpen = ragKnowledge.getRequeryOpen();
        String query = request.getQuery();
        String chatHistory = request.getChatHistory();

        if (ONE_STRING.equals(reQueryOpen) && StrUtil.isNotBlank(chatHistory) && StrUtil.isNotBlank(query)) {
            // 重写问题
            log.info("重写问题 | 查询:{}", query);
            query = BaiLianUtils.callForObject(baiLianAppConfig.getLingxiaoxiWorkspaceId(),
                    baiLianAppConfig.getLingxiaoxiChatApiKey(),
                    baiLianAppConfig.getLingxiaoxiReQueryAppId(),
                    query,
                    String.class,
                    Map.of("chatHistory", chatHistory)
                    );
            log.info("重写问题 | 结果:{}", query);
        }


        // 判断是否开启重排序
        boolean rerankEnabled = "1".equals(ragKnowledge.getRerankOpen());
        int searchTopK = rerankEnabled ? 3 * topK : topK;

        try {
            // 1. 获取查询向量
            float[] queryEmbedding = embeddingService.getEmbedding(query);
            String filterString = "label == '" + request.getLabel() + "'";

            // 2. Milvus向量搜索
            List<MilvusSearchResult> searchResults = milvusService.searchByEmbeddingWithDetails(
                    RAG_TENANT_CLIENT_KEY,
                    ragKnowledge.getCollectionName(),
                    queryEmbedding,
                    searchTopK,
                    similarityThreshold,
                    filterString
            );

            if (searchResults.isEmpty()) {
                log.info("未找到相似内容 | 查询:{}", query);
                return Collections.emptyList();
            }

            // 3. 准备文档分片数据
            List<String> splitIds = searchResults.stream()
                    .map(MilvusSearchResult::getId)
                    .collect(Collectors.toList());
            List<RagDocumentSplitPO> documentSplits = ragDocumentSplitService.listByIds(splitIds);
            Map<String, RagDocumentSplitPO> splitMap = documentSplits.stream()
                    .collect(Collectors.toMap(split -> split.getId().toString(), Function.identity()));

            // 4. 准备文档信息
            Set<Long> documentIds = searchResults.stream()
                    .map(MilvusSearchResult::getDocumentId)
                    .collect(Collectors.toSet());
            List<RagDocumentPO> documents = ragDocumentMapper.selectBatchIds(documentIds);
            Map<Long, RagDocumentPO> documentMap = documents.stream()
                    .collect(Collectors.toMap(RagDocumentPO::getId, Function.identity()));

            Map<String, String> fileUrlMap = request.getRagKnowledgeId() == 2 ? getFileUrlMap() : new HashMap<>(32);

            List<SimilarContentSearchResponse> results;

            if (rerankEnabled) {
                // 5a. 执行重排序逻辑
                List<org.springframework.ai.document.Document> documentsForRerank = searchResults.stream()
                        .map(result -> {
                            RagDocumentSplitPO split = splitMap.get(result.getId());
                            return new org.springframework.ai.document.Document(
                                    result.getId(),
                                    split != null ? split.getContent() : "",
                                    Map.of());
                        })
                        .collect(Collectors.toList());

                RerankResponse rerankResponse = embeddingService.getReRankResult(
                        new RerankRequest(
                                query,
                                documentsForRerank,
                                DashScopeRerankOptions.builder().withTopN(topK).build()
                        )
                );

                if (CollectionUtils.isEmpty(rerankResponse.getResults())) {
                    log.warn("重排序返回空结果");
                    return Collections.emptyList();
                }

                // 5b. 构建重排序后的响应
                Map<String, MilvusSearchResult> searchResultMap = searchResults.stream()
                        .collect(Collectors.toMap(MilvusSearchResult::getId, Function.identity()));

                results = rerankResponse.getResults().stream()
                        .map(docWithScore -> {
                            String docId = docWithScore.getOutput().getId();
                            MilvusSearchResult searchResult = searchResultMap.get(docId);
                            RagDocumentSplitPO split = splitMap.get(docId);
                            RagDocumentPO document = documentMap.get(searchResult.getDocumentId());

                            if (split == null || document == null) {
                                log.warn("数据不完整 | 片段ID:{} | 搜索结果:{} | 片段:{} | 文档:{}",
                                        docId, true, split != null, document != null);
                                return null;
                            }

                            SimilarContentSearchResponse response = new SimilarContentSearchResponse();
                            response.setId(docId);
                            response.setDocumentId(searchResult.getDocumentId());
                            response.setLabel(searchResult.getLabel());
                            response.setScore(docWithScore.getScore() != null ?
                                    docWithScore.getScore().floatValue() : searchResult.getScore());
                            response.setDocumentContent(split.getContent());
                            response.setDocumentUrl(fileUrlMap.getOrDefault(document.getFileName(), document.getFileUrl()));
                            response.setDocumentName(document.getFileName());

                            return response;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            } else {
                // 5c. 不使用重排序，直接返回Milvus搜索结果
                results = searchResults.stream()
                        .map(searchResult -> {
                            RagDocumentSplitPO split = splitMap.get(searchResult.getId());
                            RagDocumentPO document = documentMap.get(searchResult.getDocumentId());

                            if (split == null || document == null) {
                                log.warn("数据不完整 | 片段ID:{} | 搜索结果:{} | 片段:{} | 文档:{}",
                                        searchResult.getId(), true, split != null, document != null);
                                return null;
                            }

                            SimilarContentSearchResponse response = new SimilarContentSearchResponse();
                            response.setId(searchResult.getId());
                            response.setDocumentId(searchResult.getDocumentId());
                            response.setLabel(searchResult.getLabel());
                            response.setScore(searchResult.getScore());
                            response.setDocumentContent(split.getContent());
                            response.setDocumentUrl(fileUrlMap.getOrDefault(document.getFileName(), document.getFileUrl()));
                            response.setDocumentName(document.getFileName());

                            return response;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }

            return results;

        } catch (Exception e) {
            log.error("搜索相似内容失败: query={}", request.getQuery(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取文件URL映射
     */
    private Map<String, String> getFileUrlMap() {
        try {
            // 先从 Redis 中获取文件 URL 映射
            Map<String, String> fileUrlMap = (Map<String, String>) redisService.get(FILE_URL_MAP_KEY);
            if (fileUrlMap == null) {
                // 如果 Redis 中没有缓存，调用 API 获取文件列表并生成映射
                KnowledgeNewInfoRequest apiRequest = new KnowledgeNewInfoRequest();
                ApiResponse<List<KnowledgeFileInfoResponse>> apiResponse = dmsKnowledgeFeignClient.getFileList(apiRequest);

                if (apiResponse == null || apiResponse.getData() == null) {
                    log.warn("获取文件列表返回空结果");
                    fileUrlMap = new HashMap<>(16);
                } else {
                    fileUrlMap = apiResponse.getData().stream()
                            .collect(Collectors.toMap(
                                    KnowledgeFileInfoResponse::getFileName,
                                    KnowledgeFileInfoResponse::getDownloadTempUrl,
                                    (existing, replacement) -> replacement
                            ));
                }

                // 将获取到的文件 URL 映射存入 Redis，并设置过期时间
                redisService.set(FILE_URL_MAP_KEY, fileUrlMap, TTL);
            }
            return fileUrlMap;
        } catch (Exception e) {
            log.error("获取文件 URL 映射失败", e);
            return new HashMap<>(16);
        }
    }


    private DocumentParser getDocumentParser(String fileType) {
        // TODO OCR图片
        if (RagDocumentFileTypeEnum.PDF.getCode().equalsIgnoreCase(fileType)) {
            return new ApachePdfBoxDocumentParser(true);
        } else if (RagDocumentFileTypeEnum.DOC.getCode().equalsIgnoreCase(fileType) || RagDocumentFileTypeEnum.DOCX.getCode().equalsIgnoreCase(fileType)) {
            return new ApacheTikaDocumentParser(true);
        } else {
            // 默认使用Tika解析器处理未知类型
            return new ApacheTikaDocumentParser();
        }
    }

    private void createAndSaveDocumentSplits(Long documentId, List<TextSegment> segments) {
        LocalDateTime now = LocalDateTime.now();
        List<RagDocumentSplitPO> splitList = new ArrayList<>();

        log.info("开始处理文档分片，总数量: {}", segments.size());

        // 直接遍历所有segments
        for (TextSegment segment : segments) {
            if (segment.text() == null || segment.text().trim().isEmpty()) {
                continue;
            }

            // 清理文本
            String pureText = StrUtils.cleanText(segment.text());

            // 创建分片对象
            RagDocumentSplitPO split = new RagDocumentSplitPO();
            split.setDocumentId(documentId);
            split.setTenantId(15L);

            // 设置分片属性
            split.setContent(pureText);
            split.setWordCount(pureText.length());
            split.setKeywords("");
            split.setCreatedAt(now);
            split.setCreatedBy(UserThreadLocalUtil.getRealName());

            splitList.add(split);

            // 当累积的分片数量达到数据库批量保存阈值时保存
            if (splitList.size() >= Constants.BATCH_SIZE) {
                boolean batchResult = ragDocumentSplitService.batchSave(splitList);
                if (!batchResult) {
                    log.error("批量保存文档分片失败");
                }
                splitList.clear();
                log.info("已保存一批文档分片，数量: {}", Constants.BATCH_SIZE);
            }
        }

        // 保存剩余分片
        if (!splitList.isEmpty()) {
            boolean batchResult = ragDocumentSplitService.batchSave(splitList);
            if (!batchResult) {
                log.error("保存剩余文档分片失败");
            }
            log.info("保存剩余文档分片完成，数量: {}", splitList.size());
        }

        log.info("文档分片处理完成");
    }


    @Override
    public RagDocumentPO upload(RagDocumentAddRequest request) {

        // 1. 验证文件
        MultipartFile file = request.getFile();
        if (file == null || file.isEmpty()) {
            throw new BizException("上传文件不能为空");
        }

        // 2. 获取文件大小并校验
        long fileSizeBytes = file.getSize();
        long maxSizeBytes = maxUploadSize * 1024 * 1024L;

        if (fileSizeBytes > maxSizeBytes) {
            throw new BizException(String.format("文件大小超过限制，当前文件大小: %.2fMB，最大允许: %dMB",
                    fileSizeBytes / (1024.0 * 1024.0), maxUploadSize));
        }

        // 3. 获取文件名和文件类型
        String originalFilename = file.getOriginalFilename();
        String extension = FilenameUtils.getExtension(originalFilename);

        try {
            // 4. 生成唯一的文件标识符
            String key = UUID.randomUUID().toString().replace("-", "") + "." + extension;
            // 6. 上传文件
            Result<FileInfoVO> fileResult = fileStorageService.huaweiObsUpload(file, key);
            String url = fileResult.getData().getUrl();

            // 8. 设置文件URL

            // 9. 保存文档信息
            // 构建RagDocument对象
            RagDocumentPO document = new RagDocumentPO();
            document.setTenantId(15L);
            // 设置实际文件大小（MB）
            document.setFileSize(new BigDecimal(fileSizeBytes).divide(new BigDecimal(1024 * 1024), 2, RoundingMode.HALF_UP));
            document.setParseType(request.getParseType());
            document.setChunkStrategy(request.getChunkStrategy());
            document.setChunkLength(request.getChunkLength());
            document.setOverlapLength(request.getOverlapLength());
            document.setLabel(request.getLabel());
            document.setCreatedBy(UserThreadLocalUtil.getRealName());
            document.setCreatedAt(LocalDateTime.now());

            document.setCategoryId(request.getCategoryId());
            document.setFileName(originalFilename);
            document.setFileUrl(url);
            document.setFileType(extension);
            document.setParseStatus(RagDocumentParseStatusEnum.UN_PARSING.getCode());
            document.setObjectKey(key);

            this.save(document);
            return document;

        } catch (Exception e) {
            log.error("文件上传处理失败: ", e);
            throw new BizException("文件上传处理失败: " + e.getMessage());
        }
    }

    @Override
    public Long deleteDocumentVectors(Long documentId) {

        RagDocumentPO document = ragDocumentMapper.getDetailById(documentId);

        log.info("开始删除文档向量索引: documentId={}", documentId);

        try {
            // 获取文档片段ID列表
            List<RagDocumentSplitPO> documentSplits = ragDocumentSplitService.getByDocumentId(documentId);

            if (documentSplits.isEmpty()) {
                throw new BizException("文档没有已向量化的片段: documentId=" + documentId);
            }

            // 提取分片ID列表
            List<String> splitIds = documentSplits.stream()
                    .map(RagDocumentSplitPO::getId)
                    .map(String::valueOf)
                    .collect(Collectors.toList());


            // 从向量数据库删除索引
            String collectionName = document.getCollectionName();

            if (collectionName == null || collectionName.trim().isEmpty()) {
                throw new BizException("文档集合名称为空: documentId=" + documentId);
            }

            long deleteResult = milvusService.deleteByIdsNew(collectionName, splitIds);
            document.setParseStatus(RagDocumentParseStatusEnum.PARSED.getCode());
            this.updateById(document);
            return deleteResult;
        } catch (Exception e) {
            throw new BizException("删除文档向量索引异常: " + e.getMessage(), e);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDocumentSplits(Long documentId) {
        // 1. 校验参数
        if (documentId == null) {
            throw new BizException("文档ID不能为空");
        }

        RagDocumentPO document = this.getById(documentId);
        if (document == null) {
            throw new BizException("文档不存在: documentId=" + documentId);
        }

        // 2. 检查文档向量化状态，只有未向量化或向量化失败的文档才能删除切片
        if (RagDocumentParseStatusEnum.VECTOR_SUCCESS.getCode().equals(document.getParseStatus())) {
            throw new BizException("文档已成功向量化，不能删除切片，请先删除向量索引: documentId=" + documentId);
        }

        log.info("开始删除文档切片信息: documentId={}", documentId);

        try {
            // 3. 执行删除
            ragDocumentSplitService.deleteByDocumentId(documentId);

            // 4. 更新文档状态
            document.setParseStatus(RagDocumentParseStatusEnum.UN_PARSING.getCode());
            this.updateById(document);

            log.info("文档切片信息删除成功: documentId={}", documentId);
            return true;
        } catch (Exception e) {
            log.error("删除文档切片信息异常: documentId={}", documentId, e);
            throw new BizException("删除文档切片信息异常: " + e.getMessage(), e);
        }
    }

    @Override
    public void download(Long documentId, HttpServletResponse response) {
        RagDocumentPO document = getById(documentId);
        fileStorageService.huaweiObsDownloadFile(document.getFileName(), document.getObjectKey(), response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateByIdTran(RagDocumentPO document) {
        this.updateById(document);
    }


    @Override
    public RagDocumentProcessAllResponse processDocumentByUrl(RagDocumentProcessByUrlRequest request) {
        log.info("开始基于URL的文档一体化处理: categoryId={}, ragKnowledgeId={}, fileUrl={}",
                request.getCategoryId(), request.getRagKnowledgeId(), request.getFileUrl());

        RagDocumentProcessAllResponse response = new RagDocumentProcessAllResponse();
        response.setRagKnowledgeId(request.getRagKnowledgeId());
        response.setUploadSuccess(false);
        response.setSplitSuccess(false);
        response.setBindSuccess(false);
        response.setVectorSuccess(false);

        Long documentId;

        try {
            // 1. 直接保存文档记录
            log.info("步骤1: 开始保存文档记录");
            documentId = saveDocumentRecord(request, response);
            log.info("步骤1: 文档记录保存成功, documentId={}", documentId);

            // 2. 文档切分
            log.info("步骤2: 开始文档切分");
            splitDocumentSimple(documentId, response);
            log.info("步骤2: 文档切分成功");

            // 3. 绑定知识库
            log.info("步骤3: 开始绑定知识库");
            bindDocumentSimple(documentId, request.getRagKnowledgeId(), response);
            log.info("步骤3: 知识库绑定成功");

            // 4. 向量化
            log.info("步骤4: 开始向量化");
            vectorizeDocumentSimple(documentId, response);
            log.info("步骤4: 向量化成功");

            response.setStatusMessage("基于URL的文档一体化处理完成");

        } catch (Exception e) {
            log.error("基于URL的文档一体化处理失败", e);
            response.setErrorMessage(e.getMessage());

            // 根据处理进度设置状态描述
            if (!response.getUploadSuccess()) {
                response.setStatusMessage("保存文档记录阶段失败");
            } else if (!response.getSplitSuccess()) {
                response.setStatusMessage("切分阶段失败");
            } else if (!response.getBindSuccess()) {
                response.setStatusMessage("绑定知识库阶段失败");
            } else if (!response.getVectorSuccess()) {
                response.setStatusMessage("向量化阶段失败");
            }

            throw new BizException("基于URL的文档一体化处理失败: " + e.getMessage());
        }

        log.info("基于URL的文档一体化处理完成: documentId={}, splitCount={}",
                response.getDocumentId(), response.getSplitCount());
        return response;
    }

    /**
     * 保存文档记录
     */
    private Long saveDocumentRecord(RagDocumentProcessByUrlRequest request, RagDocumentProcessAllResponse response) {
        RagDocumentPO document = new RagDocumentPO();

        // 设置基本信息
        document.setTenantId(15L);
        document.setCategoryId(request.getCategoryId());
        document.setFileName(request.getFileName());
        document.setFileType(request.getFileType());
        document.setFileUrl(request.getFileUrl());
        document.setObjectKey(request.getObjectKey());

        // 设置文件大小
        if (request.getFileSize() != null) {
            document.setFileSize(new BigDecimal(request.getFileSize()));
        } else {
            document.setFileSize(BigDecimal.ZERO);
        }

        // 设置解析状态
        document.setParseStatus(RagDocumentParseStatusEnum.UN_PARSING.getCode());

        // 设置切分配置
        document.setChunkStrategy(request.getChunkStrategy());
        document.setChunkSeparator(request.getChunkSeparator());
        document.setChunkLength(request.getChunkLength());
        document.setOverlapLength(request.getOverlapLength());
        document.setLabel(request.getLabel());

        // 设置时间和用户信息
        LocalDateTime now = LocalDateTime.now();
        document.setCreatedAt(now);
        document.setUpdatedAt(now);
        document.setCreatedBy(UserThreadLocalUtil.getRealName());

        // 保存到数据库
        boolean saveResult = this.save(document);
        if (!saveResult) {
            throw new BizException("保存文档记录失败");
        }

        response.setDocumentId(document.getId());
        response.setDocumentName(document.getFileName());
        response.setUploadSuccess(true);

        return document.getId();
    }

    /**
     * 简化的文档切分
     */
    private void splitDocumentSimple(Long documentId, RagDocumentProcessAllResponse response) {
        this.splitDocument(documentId);
        response.setSplitSuccess(true);

        // 获取切分数量
        RagDocumentSplitPO queryParam = new RagDocumentSplitPO();
        queryParam.setDocumentId(documentId);
        List<RagDocumentSplitPO> splits = ragDocumentSplitService.getList(queryParam);
        response.setSplitCount(splits.size());
    }

    /**
     * 简化的绑定知识库
     */
    private void bindDocumentSimple(Long documentId, Long ragKnowledgeId, RagDocumentProcessAllResponse response) {
        RagKnowledgeDocumentBindRequest bindRequest = new RagKnowledgeDocumentBindRequest();
        bindRequest.setRagKnowledgeId(ragKnowledgeId);
        bindRequest.setDocumentIds(List.of(documentId));

        RagKnowledgeDocumentBindResponse bindResponse = ragKnowledgeDocumentJoinsService.bindDocuments(bindRequest);
        if (bindResponse.getSuccessCount() > 0) {
            response.setBindSuccess(true);
            response.setKnowledgeName(bindResponse.getKnowledgeName());
        } else {
            throw new BizException("知识库绑定失败: " + String.join(", ", bindResponse.getFailReasons()));
        }
    }

    /**
     * 简化的向量化
     */
    private void vectorizeDocumentSimple(Long documentId, RagDocumentProcessAllResponse response) {
        boolean vectorResult = this.vectorDocumentSplits(documentId);
        if (vectorResult) {
            response.setVectorSuccess(true);
        } else {
            throw new BizException("向量化失败");
        }
    }

    @Override
    public String syncKnowledgeDocuments(SyncKnowledgeDocumentsRequest request) {
        log.info("开始同步知识中心文档: knowledgeType={}, knowledgeSubType={}",
                request.getKnowledgeType(), request.getKnowledgeSubType());

        // 2. 调用知识中心API获取文档列表
        KnowledgeNewInfoRequest apiRequest = new KnowledgeNewInfoRequest();
        apiRequest.setKnowledgeType(request.getKnowledgeType());
        apiRequest.setKnowledgeSubType(request.getKnowledgeSubType());

        log.info("调用知识中心API获取文档列表:{}", JSONUtil.toJsonStr(apiRequest));
        ApiResponse<List<KnowledgeFileInfoResponse>> apiResponse = dmsKnowledgeFeignClient.getFileList(apiRequest);
        log.info("知识中心API返回数据:{}", JSONUtil.toJsonStr(apiResponse));

        if (apiResponse == null || 200 != apiResponse.getCode()) {
            throw new BizException("调用知识中心API失败，返回数据为空");
        }

        List<KnowledgeFileInfoResponse> knowledgeFiles = apiResponse.getData();
        if (CollUtil.isEmpty(knowledgeFiles)) {
            return "知识中心返回的文档列表为空";
        }


        // 3. 查询已存在的文档（根据fileName过滤）
        List<String> fileNames = knowledgeFiles.stream()
                .map(KnowledgeFileInfoResponse::getFileName)
                .filter(StrUtil::isNotBlank)
                .toList();

        LambdaQueryWrapper<RagDocumentPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(RagDocumentPO::getFileName, fileNames);
        List<RagDocumentPO> existingDocuments = this.list(queryWrapper);

        Set<String> existingFileNames = existingDocuments.stream()
                .map(RagDocumentPO::getFileName)
                .collect(Collectors.toSet());

        log.info("数据库中已存在{}个同名文档", existingFileNames.size());

        // 4. 过滤出新文档
        List<KnowledgeFileInfoResponse> newFiles = knowledgeFiles.stream()
                .filter(file -> StrUtil.isNotBlank(file.getFileName()) && !existingFileNames.contains(file.getFileName()))
                .toList();


        log.info("过滤出{}个新文档需要处理", newFiles.size());

        if (CollUtil.isEmpty(newFiles)) {
            return "没有新文档需要同步";
        }

        // 5. 批量处理新文档
        this.processNewDocuments(newFiles, request);


        return "文档同步完成";
    }

    /**
     * 处理新文档
     *
     * @param newFiles 新文档列表
     * @param request  同步请求
     */
    private void processNewDocuments(List<KnowledgeFileInfoResponse> newFiles,
                                     SyncKnowledgeDocumentsRequest request) {
        log.info("开始批量处理{}个新文档", newFiles.size());


        LocalDateTime now = LocalDateTime.now();
        String currentUser = "system";

        // 1. 批量创建文档记录
        List<RagDocumentPO> documentsToSave = new ArrayList<>();
        for (KnowledgeFileInfoResponse file : newFiles) {
            RagDocumentPO document = createDocumentFromKnowledgeFile(file, request, now, currentUser);
            documentsToSave.add(document);
        }

        // 2. 批量保存文档记录
        if (CollUtil.isNotEmpty(documentsToSave)) {
            this.saveBatch(documentsToSave);
        }

        // 3. 逐个处理文档（解析、切分、绑定、向量化）
        for (RagDocumentPO document : documentsToSave) {
            processDocumentComplete(document, request);
        }
    }

    /**
     * 从知识中心文件信息创建文档记录
     */
    private RagDocumentPO createDocumentFromKnowledgeFile(KnowledgeFileInfoResponse file,
                                                          SyncKnowledgeDocumentsRequest request,
                                                          LocalDateTime now,
                                                          String currentUser) {
        if (StrUtil.isBlank(file.getFileName())) {
            throw new BizException("文件名不能为空");
        }

        RagDocumentPO document = new RagDocumentPO();
        document.setTenantId(15L);
        document.setCategoryId(request.getRagCategoryId());
        document.setFileName(file.getFileName());

        // 从文件名提取文件类型
        String fileType = FilenameUtils.getExtension(file.getFileName());
        if (StrUtil.isBlank(fileType)) {
            fileType = "unknown";
        }
        document.setFileType(fileType.toLowerCase());

        // 设置文件大小
        if (file.getFileSize() != null && file.getFileSize() > 0) {
            document.setFileSize(new BigDecimal(file.getFileSize()).divide(new BigDecimal(1024 * 1024), 2, RoundingMode.HALF_UP));
        } else {
            document.setFileSize(BigDecimal.ZERO);
        }

        // 设置文件URL - 优先使用downloadTempUrl，其次使用fileFullPath
        String fileUrl = file.getDownloadTempUrl();
        if (StrUtil.isBlank(fileUrl)) {
            throw new BizException("文件URL为空，无法处理文档: " + file.getFileName());
        }
        document.setFileUrl(fileUrl);

        // 设置解析状态为未解析
        document.setParseStatus(RagDocumentParseStatusEnum.UN_PARSING.getCode());

        // 设置切分配置
        document.setChunkStrategy("01");
        document.setChunkLength(request.getChunkLength());
        document.setOverlapLength(request.getChunkOverlap());
        Integer knowledgeSubType = request.getKnowledgeSubType();
        document.setLabel(TrainingTypeEnum.getByCode(String.valueOf(knowledgeSubType)));
        document.setBizInfo("旗宝");

        // 设置时间和用户信息
        document.setCreatedAt(now);
        document.setCreatedBy(currentUser);
        document.setUpdatedAt(now);
        document.setUpdatedBy(currentUser);

        return document;
    }


    /**
     * 处理单个文档的完整流程（解析、切分、绑定、向量化）
     */
    private void processDocumentComplete(RagDocumentPO document, SyncKnowledgeDocumentsRequest request) {
        Long documentId = document.getId();
        try {
            // 1. 文档解析和切分
            log.info("开始解析文档: documentId={}, fileName={}", documentId, document.getFileName());
            this.splitDocument(documentId);
            log.info("文档解析切分成功: documentId={}", documentId);

            // 2. 绑定知识库
            log.info("开始绑定知识库: documentId={}, ragKnowledgeId={}", documentId, request.getRagCategoryId());
            RagKnowledgeDocumentBindRequest bindRequest = new RagKnowledgeDocumentBindRequest();
            bindRequest.setRagKnowledgeId(request.getRagCategoryId());
            bindRequest.setDocumentIds(List.of(documentId));
            ragKnowledgeDocumentJoinsService.bindDocuments(bindRequest);
            log.info("知识库绑定成功: documentId={}", documentId);

            // 3. 向量化
            log.info("开始向量化: documentId={}", documentId);
            boolean vectorResult = this.vectorDocumentSplits(documentId);
            if (!vectorResult) {
                throw new BizException("向量化失败");
            }
            log.info("向量化成功: documentId={}", documentId);

        } catch (Exception e) {
            log.error("文档处理失败: documentId={}, fileName={}", documentId, document.getFileName(), e);
            throw new BizException("文档处理失败: " + e.getMessage(), e);
        }
    }
}