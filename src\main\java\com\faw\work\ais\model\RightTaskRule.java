package com.faw.work.ais.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
@Data
@TableName("rigth_task_rule")
public class RightTaskRule {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer aiPass;
    private Integer humanPass;
    private Integer aiReject;
    private Integer humanReject;
    private String systemId;
    private String bizType;
    private String checkDate;
}
