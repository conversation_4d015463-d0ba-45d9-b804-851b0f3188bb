package com.faw.work.ais.common.dto.chat;

import lombok.Data;

/**
 * 一键维保响应实体
 *
 * <AUTHOR>
 * @since 2025-06-25 11:22
 */
@Data
public class MaintenanceEntity {

    /**
     * 经销商编码
     */
    private String dealerCode;

    /**
     * 经销商名称
     */
    private String dealerName;

    /**
     * 最近进厂经销商代码
     */
    private String lastEnterDealerCode;

    /**
     * 最近进厂经销商名称
     */
    private String lastEnterDealerName;

    /**
     * 最近进厂时间
     */
    private String lastEnterTime2;

    /**
     * 车牌号
     */
    private String vehicleNumber;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 报修次数
     */
    private Integer repairTimes;

    /**
     * 预约日期
     */
    private String appointmentDate;

    /**
     * 预约时间
     */
    private String appointmentDateTime;

    /**
     * 车辆vin
     */
    private String vin;

    /**
     * 用户ID
     */
    private String aid;

    /**
     * 车系
     */
    private String carSeries;

    /**
     * 车辆里程
     */
    private String mileage;

    /**
     * 距上次保养里程
     */
    private String mileageEnd;

    /**
     * 上次进厂时间
     */
    private String lastEnterTime;

}