package com.faw.work.ais.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * ai审核点关系表
 * Created  by Mr.hp
 * DateTime on 2025-05-08 10:50:12
 *
 * <AUTHOR> Mr.hp
 * @ApiModel(value = "AiAuditModelPoint", description = "ai审核点关系表")
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "人工智能ai审核点关系表", name = "人工智能ai审核点关系表")
public class AiAuditModelPoint implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     *
     * @ApiModelProperty(value = "主键id")
     */
    @Schema(description = "主键id")
    private Long id;

    /**
     * 主表id
     *
     * @ApiModelProperty(value = "主表id")
     */
    @Schema(description = "主表id")
    private Long auditId;

    /**
     * 审核点名称
     *
     * @ApiModelProperty(value = "审核点名称")
     */
    @Schema(description = "审核点名称")
    private String auditPointName;

    /**
     * 创建时间
     *
     * @ApiModelProperty(value = "创建时间")
     */
    @Schema(description = "创建时间")
    private String createTime;

    /**
     * 创建人编码
     *
     * @ApiModelProperty(value = "创建人编码")
     */
    @Schema(description = "创建人编码")
    private String createUserCode;

    /**
     * 创建人姓名
     *
     * @ApiModelProperty(value = "创建人姓名")
     */
    @Schema(description = "创建人姓名")
    private String createUserName;

    /**
     * 更新时间
     *
     * @ApiModelProperty(value = "更新时间")
     */
    @Schema(description = "更新时间")
    private String updateTime;

    /**
     * 更新人编码
     *
     * @ApiModelProperty(value = "更新人编码")
     */
    @Schema(description = "更新人编码")
    private String updateUserCode;

    /**
     * 更新人姓名
     *
     * @ApiModelProperty(value = "更新人姓名")
     */
    @Schema(description = "更新人姓名")
    private String updateUserName;


}
