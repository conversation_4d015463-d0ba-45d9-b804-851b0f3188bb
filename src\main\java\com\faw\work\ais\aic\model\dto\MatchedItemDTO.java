package com.faw.work.ais.aic.model.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 命中的质检项详情
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "命中的质检项详情")
public class MatchedItemDTO {

    @Schema(description = "质检项类型（keyword:关键词, similar:相似度, negative:负面, voice:语音）", example = "keyword")
    private String type;

    @Schema(description = "命中的内容或规则", example = "试驾礼品")
    private String content;

    @Schema(description = "是否命中该项", example = "true")
    private Boolean matched;
}

