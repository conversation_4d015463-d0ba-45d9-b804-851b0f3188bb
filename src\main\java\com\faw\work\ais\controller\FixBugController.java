package com.faw.work.ais.controller;

import com.faw.work.ais.entity.dto.ai.FixBugRequest;
import com.faw.work.ais.service.FixBugService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 问题修复 控制类
 *
 * <AUTHOR>
 * @since 2025-01-17 14:45
 */
@Tag(name = "问题修复 控制类")
@RestController
@RequestMapping("/fixBug")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FixBugController {

    private final FixBugService fixBugService;


    @Operation(summary = "国补问题修复")
    @PostMapping(value = "/subsidy")
    public String subsidyFixBug(@RequestBody FixBugRequest request) {
        return fixBugService.subsidyFixBug(request);
    }

}
